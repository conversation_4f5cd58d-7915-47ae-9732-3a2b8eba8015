import { call, put, takeLatest, delay } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  loginRequest,
  loginSuccess,
  loginFailure,
  registerRequest,
  registerSuccess,
  registerFailure,
  logout
} from '../slices/authSlice';
import { connectRequest, disconnectRequest } from '../slices/socketSlice';
import { authService } from '../../services/authService';

// Worker saga for login
function* loginSaga(action: PayloadAction<{ username: string; password: string }>) {
  try {
    const { username, password } = action.payload;

    // Call API to login
    const response = yield call(authService.login, username, password);
console.log('response', response)
    // Dispatch success action with token
    yield put(loginSuccess({
      access_token: response.access_token,
      username: username
    }));

    // Connect to socket after login
    yield delay(500); // Small delay to ensure auth state is updated
    yield put(connectRequest({ authToken: response.access_token }));
  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(loginFailure(error.message || 'Login failed'));
  }
}

// Worker saga for registration
function* registerSaga(action: PayloadAction<{ username: string; password: string }>) {
  try {
    const { username, password } = action.payload;

    // Call API to register
    const response = yield call(authService.register, username, password);

    // Dispatch success action with token
    yield put(registerSuccess({
      access_token: response.access_token,
      username: username
    }));

    // Connect to socket after registration
    yield delay(500); // Small delay to ensure auth state is updated
    yield put(connectRequest({ authToken: response.access_token }));
  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(registerFailure(error.message || 'Registration failed'));
  }
}

// Worker saga for logout
function* logoutSaga() {
  try {
    // Disconnect socket when user logs out
    yield put(disconnectRequest());
    console.log('Socket disconnected after logout');
  } catch (error: any) {
    console.error('Logout error:', error);
  }
}

// Root auth saga
export function* authSaga() {
  yield takeLatest(loginRequest.type, loginSaga);
  yield takeLatest(registerRequest.type, registerSaga);
  yield takeLatest(logout.type, logoutSaga);
}

import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { NotFoundException } from '@nestjs/common';

describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  beforeEach(async () => {
    const mockUsersService = {
      findUserIdByUsername: jest.fn(),
      findUserById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('resolveUsername', () => {
    it('should return userId when username exists', async () => {
      const username = 'testuser';
      const userId = '123e4567-e89b-12d3-a456-426614174000';
      
      jest.spyOn(service, 'findUserIdByUsername').mockResolvedValue(userId);
      
      const result = await controller.resolveUsername(username);
      
      expect(result).toEqual({ userId });
      expect(service.findUserIdByUsername).toHaveBeenCalledWith(username);
    });

    it('should throw NotFoundException when username does not exist', async () => {
      const username = 'nonexistentuser';
      
      jest.spyOn(service, 'findUserIdByUsername').mockRejectedValue(
        new NotFoundException(`User with username "${username}" not found`),
      );
      
      await expect(controller.resolveUsername(username)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getUserById', () => {
    it('should return user when userId exists', async () => {
      const userId = '123e4567-e89b-12d3-a456-426614174000';
      const user = {
        id: userId,
        username: 'testuser',
      };
      
      jest.spyOn(service, 'findUserById').mockResolvedValue(user);
      
      const result = await controller.getUserById(userId);
      
      expect(result).toEqual(user);
      expect(service.findUserById).toHaveBeenCalledWith(userId);
    });

    it('should throw NotFoundException when userId does not exist', async () => {
      const userId = '123e4567-e89b-12d3-a456-426614174999';
      
      jest.spyOn(service, 'findUserById').mockRejectedValue(
        new NotFoundException(`User with ID "${userId}" not found`),
      );
      
      await expect(controller.getUserById(userId)).rejects.toThrow(NotFoundException);
    });
  });
});

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 95%;
  max-width: 350px;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

@media (min-width: 768px) {
  .modal-content {
    width: 90%;
    max-width: 450px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

@media (min-width: 768px) {
  .modal-header {
    padding: 16px 20px;
  }
}

.modal-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333333;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #757575;
  padding: 0;
  margin: 0;
  line-height: 1;
}

.close-button:hover {
  color: #333333;
}

.modal-body {
  padding: 16px;
}

@media (min-width: 768px) {
  .modal-body {
    padding: 20px;
  }
}

.warning-text {
  font-weight: 600;
  margin-bottom: 12px;
  color: #d32f2f;
}

.info-text {
  margin-bottom: 0;
  color: #757575;
  font-size: 0.9rem;
  line-height: 1.4;
}

.modal-footer {
  padding: 12px 16px;
}

@media (min-width: 768px) {
  .modal-footer {
    padding: 16px 20px;
  }
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333333;
  border: 1px solid #d0d0d0;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  min-width: 100px;
}

.cancel-button:hover:not(:disabled) {
  background-color: #e8e8e8;
}

.delete-button {
  background-color: #d32f2f;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 45px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  min-width: 100px;
}

.delete-button:hover:not(:disabled) {
  background-color: #b71c1c;
}

.cancel-button:disabled,
.delete-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

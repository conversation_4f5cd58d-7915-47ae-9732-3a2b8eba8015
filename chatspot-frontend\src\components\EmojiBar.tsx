import React, { useState, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFaceSmile,
  faFaceLaughBeam,
  faFaceAngry,
  faFaceSadTear,
  faHeart,
  faThumbsUp
} from '@fortawesome/free-solid-svg-icons';
import './EmojiBar.css';

interface EmojiBarProps {
  onEmojiClick: (emoji: string) => void;
  onEmojiLongPress?: (emoji: string) => void;
  onEmojiRelease?: () => void;
}

const EmojiBar: React.FC<EmojiBarProps> = ({
  onEmojiClick,
  onEmojiLongPress,
  onEmojiRelease
}) => {
  // Define emoji mapping for FontAwesome icons to emoji strings with mood names
  const emojis = [
    { icon: faFaceSmile, text: '😊', mood: 'happy' },
    { icon: faFaceLaughBeam, text: '😂', mood: 'laughing' },
    { icon: faFaceAngry, text: '😡', mood: 'angry' },
    { icon: faFaceSadTear, text: '😢', mood: 'sad' },
    { icon: faHeart, text: '❤️', mood: 'love' },
    { icon: faThumbsUp, text: '👍', mood: 'thumbsUp' }
  ];

  // Long press handling
  const [activeEmojiIndex, setActiveEmojiIndex] = useState<number | null>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const longPressDuration = 500; // ms

  // Handle touch/mouse events for long press
const handleStart = (emoji: string, index: number, event?: React.TouchEvent | React.MouseEvent) => {
  if (!onEmojiLongPress) return;

  // No need for preventDefault here
  longPressTimerRef.current = setTimeout(() => {
    setActiveEmojiIndex(index);
    onEmojiLongPress(emoji);
  }, longPressDuration);
};

const handleEnd = (emoji: string, event?: React.TouchEvent | React.MouseEvent) => {
  // No preventDefault
  if (longPressTimerRef.current) {
    clearTimeout(longPressTimerRef.current);
    longPressTimerRef.current = null;
  }

  if (activeEmojiIndex !== null) {
    setActiveEmojiIndex(null);
    onEmojiRelease?.();

    setTimeout(() => {
      setActiveEmojiIndex(null);
    }, 50);
  } else {
    onEmojiClick(emoji);
  }
};

const handleCancel = (event?: React.TouchEvent | React.MouseEvent) => {
  // No preventDefault
  if (longPressTimerRef.current) {
    clearTimeout(longPressTimerRef.current);
    longPressTimerRef.current = null;
  }

  if (activeEmojiIndex !== null) {
    setActiveEmojiIndex(null);
    onEmojiRelease?.();

    setTimeout(() => {
      setActiveEmojiIndex(null);
    }, 50);
  }
};


  return (
    <div className="emoji-bar">
      {emojis.map((emoji, index) => (
        <button
          key={index}
          className={`emoji-button ${activeEmojiIndex === index ? 'long-press-active' : ''}`}
          onMouseDown={(e) => handleStart(emoji.text, index, e)}
          onMouseUp={(e) => handleEnd(emoji.text, e)}
          onMouseLeave={(e) => handleCancel(e)}
          onTouchStart={(e) => handleStart(emoji.text, index, e)}
          onTouchEnd={(e) => handleEnd(emoji.text, e)}
          onTouchCancel={(e) => handleCancel(e)}
          aria-label={`Insert ${emoji.text} emoji`}
        >
          <FontAwesomeIcon icon={emoji.icon} />
        </button>
      ))}
    </div>
  );
};

export default EmojiBar;

import React from 'react';
import { withObservables } from '@nozbe/watermelondb/react';
import RoomListItem from './RoomListItem';
import { database } from '../database/config';
import { map } from 'rxjs/operators';

interface EnhancedRoomListItemProps {
  roomId: string;
  onPress: () => void;
  room?: any; // This will be injected by withObservables
}

// The base component that receives the observed room
const RoomItemBase = ({ room, onPress }: EnhancedRoomListItemProps) => {
  // Add debug logging to see when component renders and with what data
  console.log('RoomItemBase rendering:', room ? `${room.id} (${room._timestamp})` : 'null');

  if (!room) {
    return null;
  }

  // Format the timestamp based on how recent it is
  const messageDate = new Date(room.updated);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  let formattedTime = '';
  if (messageDate.toDateString() === today.toDateString()) {
    // Today - show time
    formattedTime = messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (messageDate.toDateString() === yesterday.toDateString()) {
    // Yesterday
    formattedTime = 'Yesterday';
  } else {
    // Older - show date
    formattedTime = messageDate.toLocaleDateString();
  }

  // Use the properties from our enhanced room object
  const otherUsername = room.otherUsername || '';
  const lastMessage = room.lastMessage || 'No messages yet';
  const unreadCount = room.unreadCount || 0;

  return (
    <RoomListItem
      username={otherUsername}
      lastMessage={lastMessage}
      timestamp={formattedTime}
      unreadCount={unreadCount}
      onPress={onPress}
    />
  );
};

// Enhance the component with withObservables to make it reactive
const enhance = withObservables(['roomId'], ({ roomId }) => {
  // Get the rooms collection
  const roomsCollection = database.collections.get('rooms');

  // Observe the specific room by ID
  return {
    room: roomsCollection
      .findAndObserve(roomId)
      .pipe(
        // Force the component to re-render when any field changes
        map(room => {
          console.log('Room updated:', roomId, room ? room.id : 'null');

          // Create a new object to ensure React detects the change
          if (room) {
            // Cast to any to avoid TypeScript errors
            const roomObj = room as any;

            // Add a timestamp to force React to see this as a new object
            return {
              id: roomObj.id,
              roomId: roomObj.roomId,
              username: roomObj.username,
              otherUsername: roomObj.otherUsername,
              lastMessage: roomObj.lastMessage,
              updated: roomObj.updated,
              unreadCount: roomObj.unreadCount,
              _timestamp: Date.now()
            };
          }
          return null;
        })
      )
  };
});

// Export the enhanced component
export default enhance(RoomItemBase);

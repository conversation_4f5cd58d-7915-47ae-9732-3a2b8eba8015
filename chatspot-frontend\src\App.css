#root {
  width: 100%;
  margin: 0 auto;
  height: auto;
}

/* Adjust root container for larger screens */
@media (min-width: 768px) {
  #root {
    max-width: 1280px;
  }
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  width: 100%;
}



.app-header {
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem;
  text-align: center;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  margin-bottom: 12px;
}

.app-header h1 {
  margin: 0;
  font-size: 1.4rem;
}

@media (min-width: 768px) {
  .app-header {
    padding: 1rem;
    margin-bottom: 20px;
  }

  .app-header h1 {
    font-size: 1.8rem;
  }
}

.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow: visible;
  width: 100%;
}

@media (min-width: 768px) {
  .app-content {
    gap: 20px;
  }
}

.connection-section {
  margin-bottom: 20px;
}

.messaging-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 20px;
  overflow: visible;
}

.message-input-container {
  flex-shrink: 0;
}

.chat-window-container {
  flex: 1;
  min-height: 300px;
  overflow: visible;
}

.app-footer {
  text-align: center;
  padding: 0.75rem;
  border-radius: 0 0 var(--radius-md) var(--radius-md);
  color: #666;
  font-size: 0.85rem;
}

@media (min-width: 768px) {
  .app-footer {
    padding: 1rem;
    font-size: 0.9rem;
  }
}

/* Mobile-first layout (default) */
.messaging-section {
  flex-direction: column;
  min-height: 300px;
}

.message-input-container {
  width: 100%;
  border-top:1px solid #eee ;
}

.chat-window-container {
  width: 100%;
  min-height: 250px;
}

/* Responsive adjustments for desktop */
@media (min-width: 768px) {
  .messaging-section {
    flex-direction: row;
    min-height: 500px;
  }

  .message-input-container {
    width: 100%;
  }

  .chat-window-container {
    width: 100%;
    min-height: 300px;
  }
}

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AuthState {
  user: string | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

interface LoginSuccessPayload {
  access_token: string;
  username: string;
}

// Initial state will be populated from AsyncStorage in the app initialization
const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Login actions
    loginRequest: (state, action: PayloadAction<{ username: string; password: string }>) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (state, action: PayloadAction<LoginSuccessPayload>) => {
      console.log('payload', action.payload)
      state.loading = false;
      state.isAuthenticated = true;
      state.token = action.payload.access_token;
      state.user = action.payload.username;
      // Store token and username in AsyncStorage for persistence
      AsyncStorage.setItem('auth_token', action.payload.access_token);
      AsyncStorage.setItem('auth_username', action.payload.username);
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Register actions
    registerRequest: (state, action: PayloadAction<{ username: string; password: string }>) => {
      state.loading = true;
      state.error = null;
    },
    registerSuccess: (state, action: PayloadAction<LoginSuccessPayload>) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.token = action.payload.access_token;
      state.user = action.payload.username;
      // Store token and username in AsyncStorage for persistence
      AsyncStorage.setItem('auth_token', action.payload.access_token);
      AsyncStorage.setItem('auth_username', action.payload.username);
    },
    registerFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Logout action
    logout: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.user = null;
      // Remove token and username from AsyncStorage
      AsyncStorage.removeItem('auth_token');
      AsyncStorage.removeItem('auth_username');
    },

    // Initialize auth from storage
    initializeAuth: (state, action: PayloadAction<{ token: string | null; username: string | null }>) => {
      if (action.payload.token && action.payload.username) {
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.username;
      }
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    }
  }
});

// Export actions
export const {
  loginRequest,
  loginSuccess,
  loginFailure,
  registerRequest,
  registerSuccess,
  registerFailure,
  logout,
  initializeAuth,
  clearError
} = authSlice.actions;

// Export selectors
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectAuthToken = (state: RootState) => state.auth.token;
export const selectUser = (state: RootState) => state.auth.user;
export const selectAuthLoading = (state: RootState) => state.auth.loading;
export const selectAuthError = (state: RootState) => state.auth.error;

// Export reducer
export default authSlice.reducer;

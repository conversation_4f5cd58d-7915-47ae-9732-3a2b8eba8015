# Production environment variables
VITE_API_URL=https://chatspot-backend-8a7y.onrender.com/
VITE_WS_URL=wss://chatspot-backend-8a7y.onrender.com/
VITE_ENV=production
VITE_DEBUG=false

# Firebase configuration for notifications
VITE_FIREBASE_API_KEY=AIzaSyDmaDkGkwup19iZ2G_kM7hof5q078yGU-s
VITE_FIREBASE_PROJECT_ID=guptmessenger-14966
VITE_FIREBASE_MESSAGING_SENDER_ID=130096585895
VITE_FIREBASE_APP_ID=1:130096585895:web:39ef91088053ec66d60f82

# Web Push certificate key pair (VAPID key) from Firebase Console
# Project Settings > Cloud Messaging > Web Configuration > Web Push certificates
VITE_FIREBASE_VAPID_KEY=BFC3QyghCDe6fKAK-ylvrKupXOCEgToba30wgvP5JUqg0ZAsTrf5a4eOGAPldSOU3ZDfPI_qWWYkTjY7IQt4oHU

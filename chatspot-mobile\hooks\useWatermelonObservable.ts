import { useState, useEffect } from 'react';
import { Observable } from 'rxjs';

/**
 * Custom hook to subscribe to a WatermelonDB observable
 * @param observable The WatermelonDB observable to subscribe to
 * @param initialValue Initial value to use before the observable emits
 * @returns The latest value emitted by the observable
 */
export function useWatermelonObservable<T>(
  observable: Observable<T> | null,
  initialValue: T
): T {
  const [value, setValue] = useState<T>(initialValue);

  useEffect(() => {
    if (!observable) {
      return;
    }

    // Create a stable reference to the subscription
    const subscription = observable.subscribe(newValue => {
      // Only update state if the value has actually changed
      setValue(prevValue => {
        // For arrays, check if length has changed (simple check)
        if (Array.isArray(prevValue) && Array.isArray(newValue)) {
          if (prevValue.length !== newValue.length) {
            return newValue;
          }
          // For simple equality check of primitive values
          if (JSON.stringify(prevValue) !== JSON.stringify(newValue)) {
            return newValue;
          }
          return prevValue;
        }
        // For non-arrays, just return the new value
        return newValue;
      });
    });

    // Clean up subscription when component unmounts or observable changes
    return () => {
      subscription.unsubscribe();
    };
  }, [observable]);

  return value;
}

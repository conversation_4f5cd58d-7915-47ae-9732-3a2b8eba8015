import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import Box from './Box';
import Text from './Text';
import UserInfo from './UserInfo';

interface RoomListItemProps {
  username: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  onPress: () => void;
}

export const RoomListItem = ({
  username,
  lastMessage,
  timestamp,
  unreadCount,
  onPress,
}: RoomListItemProps) => {
  const theme = useTheme<Theme>();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { borderBottomColor: theme.colors.border }
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Box flex={1} flexDirection="row" alignItems="center">
        <UserInfo username={username} size="medium" showName={false} />

        <Box flex={1} marginLeft="s">
          <Box flexDirection="row" justifyContent="space-between" alignItems="center">
            <Text variant="bodyBold" numberOfLines={1} flex={1}>
              {username}
            </Text>
            <Text variant="caption" color="textSecondary">
              {timestamp}
            </Text>
          </Box>

          <Box flexDirection="row" justifyContent="space-between" alignItems="center" marginTop="xs">
            <Text
              variant="caption"
              color="textSecondary"
              numberOfLines={1}
              style={styles.lastMessage}
            >
              {lastMessage}
            </Text>

            {unreadCount > 0 && (
              <Box
                backgroundColor="primary"
                borderRadius="full"
                paddingHorizontal="s"
                paddingVertical="xs"
                minWidth={20}
                height={20}
                justifyContent="center"
                alignItems="center"
              >
                <Text color="white" fontSize={12} fontWeight="bold">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </Text>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  lastMessage: {
    flex: 1,
    marginRight: 8,
  },
});

export default RoomListItem;

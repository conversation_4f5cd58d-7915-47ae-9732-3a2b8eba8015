import { Box, Typography, Paper } from '@mui/material';
import AdminLayout from '../components/AdminLayout';

const MessagesPage = () => {
  return (
    <AdminLayout title="Message Management">
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Typography variant="h5" gutterBottom>
          Message Management
        </Typography>
        
        <Paper sx={{ p: 3 }}>
          <Typography variant="body1">
            This page will allow administrators to manage messages in the ChatSpot system.
            Features to be implemented:
          </Typography>
          
          <ul>
            <li>View all messages between users</li>
            <li>Search messages by content or user</li>
            <li>Delete inappropriate messages</li>
            <li>Send system-wide announcements</li>
            <li>Monitor message activity</li>
          </ul>
          
          <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic' }}>
            This feature is currently under development.
          </Typography>
        </Paper>
      </Box>
    </AdminLayout>
  );
};

export default MessagesPage;

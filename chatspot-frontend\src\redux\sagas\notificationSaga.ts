import { takeLatest, call, put, select } from 'redux-saga/effects';
import firebaseMessagingService from '../../firebase/firebaseService';
import {
  setPermissionStatus,
  setFcmToken,
  setInitialized,
  selectFcmToken
} from '../slices/notificationSlice';
import { selectUser } from '../slices/authSlice';
import { notificationService } from '../../services/notificationService';

// Action types
const INITIALIZE_NOTIFICATIONS = 'notification/initialize';
const REQUEST_PERMISSION = 'notification/requestPermission';
const REGISTER_FCM_TOKEN = 'notification/registerToken';

// Action creators
export const initializeNotifications = () => ({ type: INITIALIZE_NOTIFICATIONS });
export const requestNotificationPermission = () => ({ type: REQUEST_PERMISSION });
export const registerFcmToken = () => ({ type: REGISTER_FCM_TOKEN });

// Initialize notifications
function* initializeNotificationsSaga() {
  try {
    console.log('Initializing Firebase Cloud Messaging after user login/registration');

    // Initialize Firebase Messaging
    const success = yield call(firebaseMessagingService.initialize);
    console.log('Firebase Messaging initialization result:', success);

    if (success) {
      // Request permission
      const permissionGranted = yield call(firebaseMessagingService.requestPermission);
      console.log('Notification permission granted:', permissionGranted);
      yield put(setPermissionStatus(permissionGranted));

      if (permissionGranted) {
        // Get FCM token
        const token = yield call(firebaseMessagingService.getToken);
        console.log('FCM token obtained:', token ? 'Yes' : 'No');
        yield put(setFcmToken(token));

        // Register token with backend
        if (token) {
          console.log('Registering FCM token with backend');
          yield call(registerFcmTokenSaga);
        }

        // FCM will handle messages automatically
        console.log('FCM initialized for offline notifications');
      }
    }

    // Mark as initialized
    yield put(setInitialized(true));
    console.log('FCM initialization process completed');
  } catch (error) {
    console.error('Failed to initialize notifications:', error);
    yield put(setInitialized(true)); // Still mark as initialized to prevent retries
  }
}

// Request notification permission
function* requestPermissionSaga() {
  try {
    const permissionGranted = yield call(firebaseMessagingService.requestPermission);
    yield put(setPermissionStatus(permissionGranted));

    if (permissionGranted) {
      // Get FCM token
      const token = yield call(firebaseMessagingService.getToken);
      yield put(setFcmToken(token));

      // Register token with backend
      if (token) {
        yield call(registerFcmTokenSaga);
      }
    }
  } catch (error) {
    console.error('Failed to request notification permission:', error);
  }
}

// Register FCM token with backend
function* registerFcmTokenSaga() {
  try {
    const token = yield select(selectFcmToken);
    const user = yield select(selectUser);

    console.log('Attempting to register FCM token with backend. Token exists:', !!token, 'User exists:', !!user);

    if (token && user) {
      // Call API to register token
      yield call(notificationService.registerToken, user, token);
      console.log('FCM token registered successfully with backend for user:', user);
    } else {
      console.log('Cannot register FCM token: missing token or user');
    }
  } catch (error) {
    console.error('Failed to register FCM token with backend:', error);
  }
}

// Notification saga
export function* notificationSaga() {
  yield takeLatest(INITIALIZE_NOTIFICATIONS as any, initializeNotificationsSaga);
  yield takeLatest(REQUEST_PERMISSION as any, requestPermissionSaga);
  yield takeLatest(REGISTER_FCM_TOKEN as any, registerFcmTokenSaga);
}

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, Link } from 'react-router-dom';
import { loginRequest, clearError, selectAuthError, selectAuthLoading, selectIsAuthenticated } from '../redux/slices/authSlice';
import './Auth.css';
import { RootState } from '../redux/store';
import ClearPreviousDataModal from './ClearPreviousDataModal';
import { checkPreviousUserData } from '../utils/userDataUtils';
import { chatDBService } from '../database/service';

const Login: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const error = useSelector(selectAuthError);
  const loading = useSelector(selectAuthLoading);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showClearDataModal, setShowClearDataModal] = useState<boolean>(false);
  const [clearingData, setClearingData] = useState<boolean>(false);
  const [previousUserData, setPreviousUserData] = useState<{
    hasPreviousData: boolean;
    previousUsername: string | null;
  }>({ hasPreviousData: false, previousUsername: null });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Use replace: true to prevent back button from returning to login
      navigate('/chat', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // Handle browser history events
  useEffect(() => {
    // Block navigation back to login page when already authenticated
    const handlePopState = () => {
      if (isAuthenticated) {
        navigate('/chat', { replace: true });
      }
    };

    // Add event listener for popstate (browser back/forward buttons)
    window.addEventListener('popstate', handlePopState);

    // Clean up
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [isAuthenticated, navigate]);

  // Clear errors and temporary credentials when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
      // Clean up temporary credentials
      localStorage.removeItem('temp_username');
      localStorage.removeItem('temp_password');
    };
  }, [dispatch]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Store username and password temporarily for the saga to use
    localStorage.setItem('temp_username', username);
    localStorage.setItem('temp_password', password);

    // Check if the username is different from the previously saved one
    const userDataCheck = checkPreviousUserData(username);

    if (userDataCheck.hasPreviousData) {
      // Different username detected, show warning modal
      setPreviousUserData(userDataCheck);
      setShowClearDataModal(true);
    } else {
      // Same username or no previous login, proceed with login
      dispatch(loginRequest());
      // The actual login is handled in the saga which has access to the username and password
      // from localStorage
    }
  };

  const handleCloseClearDataModal = () => {
    setShowClearDataModal(false);
  };

  const handleClearDataAndLogin = async () => {
    try {
      setClearingData(true);

      // Store username and password temporarily for the saga to use
      localStorage.setItem('temp_username', username);
      localStorage.setItem('temp_password', password);

      // Clear all data from the database
      const success = await chatDBService.clearAllData();

      if (success) {
        console.log('Previous user data cleared successfully');
        // Proceed with login
        dispatch(loginRequest());
        // The actual login is handled in the saga which has access to the username and password
        // from localStorage
      } else {
        console.error('Failed to clear previous user data');
      }

      setShowClearDataModal(false);
    } catch (error) {
      console.error('Error clearing previous user data:', error);
    } finally {
      setClearingData(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-form">
        <h2>Login to ChatSpot</h2>

        {error && (
          <div className="auth-error">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="username">Username:</label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={loading}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <div className="password-input">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                required
              />
              <button
                type="button"
                className="toggle-visibility"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? '🙈' : '👁️'}
              </button>
            </div>
          </div>

          <button
            type="submit"
            className="auth-button"
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>

        <div className="auth-links">
          <p>
            Don't have an account? <Link to="/register">Register</Link>
          </p>
        </div>
      </div>

      {showClearDataModal && previousUserData.previousUsername && (
        <ClearPreviousDataModal
          onClose={handleCloseClearDataModal}
          onConfirm={handleClearDataAndLogin}
          loading={clearingData}
          previousUsername={previousUserData.previousUsername}
          newUsername={username}
        />
      )}
    </div>
  );
};

export default Login;

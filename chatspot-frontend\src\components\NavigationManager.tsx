import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectIsAuthenticated, selectAuthToken } from '../redux/slices/authSlice';
import { connectRequest, selectConnected } from '../redux/slices/socketSlice';

/**
 * NavigationManager component
 * 
 * This component manages browser navigation events and prevents unwanted
 * navigation behavior like going back to login page when already authenticated.
 * It also handles socket reconnection when navigation occurs.
 */
const NavigationManager: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authToken = useSelector(selectAuthToken);
  const connected = useSelector(selectConnected);

  // Handle browser history navigation
  useEffect(() => {
    // Only add the event listener if the user is authenticated
    if (isAuthenticated) {
      // This function will run when the user tries to navigate using browser buttons
      const handlePopState = (event: PopStateEvent) => {
        // If user is authenticated and tries to go back to login page, redirect to chat
        if (location.pathname === '/login') {
          // Prevent default navigation
          event.preventDefault();
          // Navigate to chat page
          navigate('/chat', { replace: true });
        }
        
        // Ensure socket connection is maintained during navigation
        if (authToken && !connected) {
          console.log('Reconnecting socket after navigation event');
          dispatch(connectRequest({ authToken }));
        }
      };

      // Add event listener for popstate (browser back/forward buttons)
      window.addEventListener('popstate', handlePopState);
      
      // Clean up the event listener when component unmounts
      return () => {
        window.removeEventListener('popstate', handlePopState);
      };
    }
  }, [isAuthenticated, location.pathname, navigate, authToken, connected, dispatch]);

  // Redirect from login page if already authenticated
  useEffect(() => {
    if (isAuthenticated && (location.pathname === '/login' || location.pathname === '/register')) {
      navigate('/chat', { replace: true });
    }
  }, [isAuthenticated, location.pathname, navigate]);

  // This component doesn't render anything
  return null;
};

export default NavigationManager;

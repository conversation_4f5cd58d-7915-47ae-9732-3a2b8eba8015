import { call, put, takeLatest } from 'redux-saga/effects';
import { authService } from '../../services/api';
import {
  loginRequest,
  loginSuccess,
  loginFailure,
  registerRequest,
  registerSuccess,
  registerFailure,
  logout
} from '../slices/authSlice';
import { connectRequest, disconnectRequest } from '../slices/socketSlice';
import { initializeNotifications } from './notificationSaga';
import { storePreviousUsername } from '../../utils/userDataUtils';

interface AuthResponse {
  access_token: string;
  [key: string]: any;
}

// Worker saga for login
function* loginSaga() {
  try {
    // Get username and password from the Login component state
    // This is a simplified approach - in a real app, you might want to pass these values
    // as parameters to the action
    const username = localStorage.getItem('temp_username') || '';
    const password = localStorage.getItem('temp_password') || '';

    if (!username || !password) {
      throw new Error('Username or password not provided');
    }

    // Call the login API
    const response: AuthResponse = yield call(authService.login, username, password);

    // Store the current username as the previous username for future checks
    storePreviousUsername(username);

    // Dispatch success action with the token
    yield put(loginSuccess({
      access_token: response.access_token,
      username
    }));

    // Connect to socket with the token
    yield put(connectRequest({ authToken: response.access_token }));

    // Initialize Firebase Cloud Messaging after login
    yield put(initializeNotifications());

    // Clear temporary storage
    localStorage.removeItem('temp_username');
    localStorage.removeItem('temp_password');

  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(loginFailure(error.toString()));
  }
}

// Worker saga for register
function* registerSaga() {
  try {
    // Get username and password from localStorage
    const username = localStorage.getItem('temp_username') || '';
    const password = localStorage.getItem('temp_password') || '';

    if (!username || !password) {
      throw new Error('Username or password not provided');
    }

    // Call the register API
    const response: AuthResponse = yield call(authService.register, username, password);

    // Store the current username as the previous username for future checks
    storePreviousUsername(username);

    // Dispatch success action with the token
    yield put(registerSuccess({
      access_token: response.access_token,
      username
    }));

    // Connect to socket with the token
    yield put(connectRequest({ authToken: response.access_token }));

    // Initialize Firebase Cloud Messaging after registration
    yield put(initializeNotifications());

    // Clear temporary storage
    localStorage.removeItem('temp_username');
    localStorage.removeItem('temp_password');

  } catch (error: any) {
    // Dispatch failure action with error message
    yield put(registerFailure(error.toString()));
  }
}

// Worker saga for logout
function* logoutSaga() {
  try {
    // Disconnect socket when user logs out
    yield put(disconnectRequest());
  } catch (error: any) {
    console.error('Logout error:', error);
  }
}

// Watcher saga for auth actions
export function* authSaga() {
  yield takeLatest(loginRequest.type, loginSaga);
  yield takeLatest(registerRequest.type, registerSaga);
  yield takeLatest(logout.type, logoutSaga);
}

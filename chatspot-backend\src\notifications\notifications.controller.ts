import { <PERSON>, Post, Body, UseGuards, Get, Param, Logger } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { RegisterTokenDto } from './dto/register-token.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('notifications')
@Controller('api/notifications')
export class NotificationsController {
  private readonly logger = new Logger(NotificationsController.name);

  constructor(private readonly notificationsService: NotificationsService) {}

  @Post('register-token')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Register FCM token' })
  @ApiResponse({ status: 201, description: 'Token registered successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async registerToken(@Body() registerTokenDto: RegisterTokenDto) {
    this.logger.log(`Registering FCM token for user ${registerTokenDto.userId}`);
    
    const result = await this.notificationsService.registerToken(
      registerTokenDto.userId,
      registerTokenDto.token,
      registerTokenDto.deviceInfo
    );
    
    return {
      success: true,
      message: 'FCM token registered successfully',
      data: {
        id: result.id,
        username: result.username,
        created_at: result.created_at,
      },
    };
  }

  @Get('test/:username')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Send test notification' })
  @ApiResponse({ status: 200, description: 'Test notification sent' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async sendTestNotification(@Param('username') username: string) {
    this.logger.log(`Sending test notification to user ${username}`);
    
    const result = await this.notificationsService.sendNotification(
      username,
      'Test Notification',
      'This is a test notification from Chatspot',
      {
        type: 'test',
        timestamp: Date.now().toString(),
      }
    );
    
    return {
      success: result,
      message: result ? 'Test notification sent successfully' : 'Failed to send test notification',
    };
  }
}

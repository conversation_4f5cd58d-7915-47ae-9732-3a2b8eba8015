import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { adminService, User, CreateUserRequest, UpdateUserRequest } from '../../services/adminService';

interface UsersState {
  users: User[];
  selectedUser: User | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: UsersState = {
  users: [],
  selectedUser: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchAllUsers = createAsyncThunk(
  'users/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      const users = await adminService.getAllUsers();
      return users;
    } catch (error: any) {
      return rejectWithValue(error.toString());
    }
  }
);

export const fetchUserById = createAsyncThunk(
  'users/fetchById',
  async (userId: string, { rejectWithValue }) => {
    try {
      const user = await adminService.getUserById(userId);
      return user;
    } catch (error: any) {
      return rejectWithValue(error.toString());
    }
  }
);

export const createUser = createAsyncThunk(
  'users/create',
  async (userData: CreateUserRequest, { rejectWithValue }) => {
    try {
      const user = await adminService.createUser(userData);
      return user;
    } catch (error: any) {
      return rejectWithValue(error.toString());
    }
  }
);

export const updateUser = createAsyncThunk(
  'users/update',
  async ({ userId, updates }: { userId: string; updates: UpdateUserRequest }, { rejectWithValue }) => {
    try {
      const user = await adminService.updateUser(userId, updates);
      return user;
    } catch (error: any) {
      return rejectWithValue(error.toString());
    }
  }
);

export const deleteUser = createAsyncThunk(
  'users/delete',
  async (userId: string, { rejectWithValue }) => {
    try {
      await adminService.deleteUser(userId);
      return userId;
    } catch (error: any) {
      return rejectWithValue(error.toString());
    }
  }
);

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearSelectedUser: (state) => {
      state.selectedUser = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch all users
      .addCase(fetchAllUsers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = action.payload;
      })
      .addCase(fetchAllUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch user by ID
      .addCase(fetchUserById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedUser = action.payload;
      })
      .addCase(fetchUserById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create user
      .addCase(createUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users.push(action.payload);
      })
      .addCase(createUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Update user
      .addCase(updateUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
        if (state.selectedUser?.id === action.payload.id) {
          state.selectedUser = action.payload;
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Delete user
      .addCase(deleteUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = state.users.filter(user => user.id !== action.payload);
        if (state.selectedUser?.id === action.payload) {
          state.selectedUser = null;
        }
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearSelectedUser, clearError } = usersSlice.actions;

// Selectors
export const selectUsers = (state: { users: UsersState }) => state.users.users;
export const selectSelectedUser = (state: { users: UsersState }) => state.users.selectedUser;
export const selectUsersLoading = (state: { users: UsersState }) => state.users.isLoading;
export const selectUsersError = (state: { users: UsersState }) => state.users.error;

export default usersSlice.reducer;

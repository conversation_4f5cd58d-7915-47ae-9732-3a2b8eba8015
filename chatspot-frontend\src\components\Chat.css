.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0 auto;
  background-color: var(--card-background);
  border-radius: 0; /* No border radius on mobile for maximum space */
  overflow: hidden;
  position: relative;
  border: 1px solid #eee;
}

@media (min-width: 768px) {
  .chat-container {
    max-width: 960px;
  }
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  color: white;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  z-index: 10;
}

@media (min-width: 768px) {
  .chat-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;

  }
}

.chat-header h2 {
  margin: 0;
  font-size: 1.2rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (min-width: 768px) {
  .chat-header h2 {
    font-size: 1.5rem;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.header-actions {
  cursor: pointer;
}


@media (min-width: 768px) {
  .header-actions {
    gap: 0px;
    width: 100%;
  }
}

.current-user {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  font-size: 17px;
  color: var(--primary-color-tone);
}




@media (min-width: 768px) {
  .current-user {
    padding: 5px 15px;
    border-radius: 15px;
    max-width: 150px;
    font-size: 0.95rem;
  }
  .current-user:hover{
  text-decoration: underline;
}

}

.logout-button {
  display: flex;
  background-color: var(--tint-color-light);
  color: var(--shade-color);
  border: none;
  padding: 6px 10px;
  border-radius: 45px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 17px;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 100%;
}


.add-button {
  display: flex;
  background-color: var(--tint-color-light);
  color: var(--shade-color);
  border: none;
  padding: 6px 10px;
  border-radius: 45px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 2rem;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 50px;
}

.add-button:hover{
  color: var(--tint-color-light);
}


@media (min-width: 768px) {
  .logout-button {
    font-size: 0.95rem;
    height: 50px;
    width: 100%;
  }

}



.logout-button:hover {
  background-color: #f5f5f5;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  color: var(--primary-color-dark);
}

.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  border-radius: 0; /* No border radius on mobile */
  overflow: hidden;
  position: relative;
}

@media (min-width: 768px) {
  .chat-content {
    border-radius: 0 0 var(--radius-md) var(--radius-md); /* Restore border radius on desktop */
  }
}

.rooms-container {
  width: 100%;
  background-color: var(--card-background);
  border-right: none; /* No border on mobile */
  display: flex;
  flex-direction: column;
  height: auto; /* Use viewport height for better proportions */
  overflow: hidden;
  z-index: 10; /* Ensure it's above other elements */
}

@media (min-width: 768px) {
  .rooms-container {
    height: auto; /* Full height on desktop */
    border-right: 1px solid var(--border-color); /* Restore border on desktop */
  }
}

.rooms-header {
  display: grid;
  align-items: center;
  padding: 10px;
}

@media (min-width: 768px) {
  .rooms-header {
    padding: 10px 15px;
    row-gap: 10px;
  }
}

.form-group {
  position: relative;
  margin-top: 8px;
}

.form-group input {
  width: 100%;
  padding: 8px 30px 8px 10px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 17px;
  outline: none;
  transition: border-color 0.2s;
}

.form-group input:focus {
  border-color: var(--primary-color);
}

.search-clear-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 20px;
  height: 20px;
}

.search-clear-button:hover {
  color: var(--primary-color);
}

.rooms-header h3 {
  margin: 0;
  color: #333;
  font-size: 0.95rem;
}

@media (min-width: 768px) {
  .rooms-header h3 {
    font-size: 0.95rem;
  }
}

.new-chat-button {
  background-color: #c5c5c540;
  color: #333;
  border: none;
  padding: 6px 10px;
  border-radius: 45px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.85rem;
  min-height: 36px;
}

@media (min-width: 768px) {
  .new-chat-button {
    padding: 8px 25px;
    gap: 5px;
    font-size: 0.95rem;
    min-height: 44px;
  }
}

.new-chat-button:hover {
  background-color: var(--primary-color-dark);
  box-shadow: 0 2px 5px rgba(240, 79, 61, 0.4);
}

.new-chat-button span {
  font-size: 16px;

}

.messages-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  min-height: 60vh; /* Use viewport height for better proportions */
  border-left: none; /* No border on mobile */
  background-color: #fff;
  overflow-y: scroll;
}

@media (min-width: 768px) {
  .messages-section {
    min-height: 200px;
    overflow-y: hidden;
  }
}

/* Chat window and message input positioning */
.chat-window-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}


/* Empty state when no chat is selected */
.no-chat-selected {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.no-chat-content {
  text-align: center;
  max-width: 400px;
  padding: 20px;
}

.no-chat-icon {
  font-size: 64px;
  color: #F04F3D;
}

.no-chat-content h3 {
  margin-top: 0;
  color: #333;
}

.no-chat-content p {
  color: #666;
  margin-bottom: 20px;
}
/* Mobile-first styles are now the default */

.no-chat-content {
  padding: 15px;
  text-align: center;
}

.no-chat-icon {
  font-size: 48px;
  color: var(--primary-color);
}

.no-chat-content h3 {
  font-size: 1.1rem;
  margin-bottom: 8px;
}

.no-chat-content p {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.start-chat-button {
  background-color: var(--primary-color-tone);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 45px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: none;
  font-size: 0.98rem;
  height: 50px;
}

.start-chat-button:hover {
  background-color: var(--primary-color);
}

@media (min-width: 768px) {
  .no-chat-content {
    padding: 20px;
  }

  .no-chat-icon {
    font-size: 56px;
  }

  .no-chat-content h3 {
    font-size: 0.98rem;
    margin-bottom: 10px;
  }

  .no-chat-content p {
    font-size: 0.98rem;
    margin-bottom: 20px;
  }
}

/* Mobile-first layout (default) */
.chat-content {
  flex-direction: column;
}

/* Mobile view specific styles */

.mobile-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
  z-index: 10;
}

.mobile-chat-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.mobile-rooms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex: 1;
}

.mobile-header-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--primary-color);
  margin-left: 10px;
  flex: 1;
}

.back-button {
  background-color: transparent;
  border: none;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 5px;
  min-height: 40px;
  cursor: pointer;
}

.back-button:hover {
  background-color: rgba(240, 79, 61, 0.1);
}

.back-button-icon {
  font-size: 18px;
}

/* Responsive layout for larger screens */
@media (min-width: 768px) {
  .chat-content {
    flex-direction: row;
  }

  .rooms-container {
    width: 30%;
    max-width: 350px;
    height: auto; /* Reset height for desktop */
  }

  .messages-section {
    width: 70%;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .mobile-header {
    display: none; /* Hide mobile header on desktop */
  }
}

.profile-header{
    padding: 10px 12px;
    border-bottom:1px solid #eee;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .tab-header{
    color: #999;
    font-size: small;
    font-weight: 600;


  }


@media (min-width: 768px) {
  .tab-header{
      display: flex;
  align-items: center;
  justify-content: center;
      font-size: small;
    font-weight: 600;
    color: #666;
  }


  .header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }

  .notification-toggle {
    background-color: transparent;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 5px 8px;
    border-radius: var(--radius-sm);
    transition: all 0.2s;
  }

  .notification-toggle:hover {
    background-color: rgba(240, 79, 61, 0.1);
  }

  .notification-toggle.enabled {
    color: var(--primary-color);
  }

  .notification-toggle.disabled {
    color: #888;
  }
}


  .header-buttons {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
  }

@media (min-width: 768px) {
  .user-setting{
  display: flex;
  margin-left: auto;
}

.setting-button{
  margin-left: auto;
}
}
.user-setting{
  display: none;
}

import React, { useState } from 'react';
import { TouchableOpacity, StyleSheet, Modal, View, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import Box from './Box';
import Text from './Text';

interface ChatHeaderProps {
  title: string;
  showBackButton?: boolean;
  onClearChat?: () => void;
  onDeleteUser?: () => void;
}

export const ChatHeader = ({
  title,
  showBackButton = true,
  onClearChat,
  onDeleteUser
}: ChatHeaderProps) => {
  const theme = useTheme<Theme>();
  const navigation = useNavigation();
  const [showMenu, setShowMenu] = useState(false);

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleMenuPress = () => {
    setShowMenu(true);
  };

  const handleClearChat = () => {
    setShowMenu(false);
    if (onClearChat) {
      onClearChat();
    }
  };

  const handleDeleteUser = () => {
    setShowMenu(false);
    if (onDeleteUser) {
      onDeleteUser();
    }
  };

  return (
    <Box
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      backgroundColor="primary"
      paddingHorizontal="m"
      paddingVertical="m"
      height={56}
    >
      <Box flexDirection="row" alignItems="center">
        {showBackButton && (
          <TouchableOpacity
            onPress={handleBackPress}
            style={styles.iconButton}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
        )}

        <Text variant="title" color="white" numberOfLines={1} style={styles.title}>
          {title}
        </Text>
      </Box>

      <TouchableOpacity
        onPress={handleMenuPress}
        style={styles.iconButton}
      >
        <Ionicons name="ellipsis-vertical" size={24} color="white" />
      </TouchableOpacity>

      {/* Menu Modal */}
      <Modal
        visible={showMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMenu(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowMenu(false)}
        >
          <View style={[styles.menuContainer, { top: 56, right: 10 }]}>
            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleClearChat}
            >
              <Ionicons name="trash-outline" size={20} color={theme.colors.error} />
              <Text variant="body" color="error" marginLeft="s">
                Clear Chat
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleDeleteUser}
            >
              <Ionicons name="person-remove-outline" size={20} color={theme.colors.error} />
              <Text variant="body" color="error" marginLeft="s">
                Delete User
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </Box>
  );
};

const styles = StyleSheet.create({
  iconButton: {
    padding: 8,
  },
  title: {
    marginLeft: 8,
    maxWidth: 200,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  menuContainer: {
    position: 'absolute',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 8,
    minWidth: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 4,
  },
});

export default ChatHeader;

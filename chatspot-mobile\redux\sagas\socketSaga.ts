import { eventChannel, END, EventChannel } from 'redux-saga';
import { call, put, take, takeEvery, fork, cancel, cancelled, select } from 'redux-saga/effects';
import { io, Socket } from 'socket.io-client';
import {
  connectRequest,
  connectSuccess,
  connectFailure,
  disconnectRequest,
  disconnectSuccess,
  sendMessageRequest,
  sendMessageSuccess,
  sendMessageFailure,
  messageReceived,
  selectServerUrl,
  selectAuthToken
} from '../slices/socketSlice';
import { selectUser } from '../slices/authSlice';
import { setUserTyping } from '../slices/typingSlice';
import { clearCurrentReceiver } from '../slices/chatDBSlice';
import { PayloadAction } from '@reduxjs/toolkit';
import { chatDBService } from '../../database/service';

// Socket instance that will be shared across sagas
let socket: Socket;

// Create a channel for socket events
function createSocketChannel(socket: Socket) {
  return eventChannel(emit => {
    // Handle connect event
    socket.on('connect', () => {
      emit({ type: 'connect', socketId: socket.id });
    });

    // Handle disconnect event
    socket.on('disconnect', () => {
      emit({ type: 'disconnect' });
    });

    // Handle error event
    socket.on('connect_error', (error) => {
      emit({ type: 'error', error });
    });

    // Handle message event - now with message types
    socket.on('message', (data, ack) => {
      // Ensure data has a type field, default to 'text' if not specified
      if (!data.type) {
        data.type = 'text';
      }
      emit({ type: 'message', data });

      // For text messages, use the acknowledgment callback to confirm delivery
      if (data.type === 'text' && data.id && typeof ack === 'function') {
        console.log('Acknowledging message delivery for:', data.id);

        // Use the receiver_username from the message data if available
        // This is the username of the user receiving the message (current user)
        const receiverUsername = data.receiver_username || 'unknown_user';

        // Send delivery confirmation using the acknowledgment callback
        try {
          const ackData = {
            status: 'delivered',
            message_id: data.id,
            client_message_id: data.client_message_id,
            receiver_username: receiverUsername
          };
          console.log('Sending acknowledgment data:', ackData);
          ack(ackData);
        } catch (error) {
          console.error('Error sending acknowledgment:', error);
        }
      }
    });

    // Handle message delivery confirmation
    socket.on('message_delivered', (data) => {
      console.log('Message delivery confirmation received:', data);
      emit({ type: 'message_delivered', data });
    });

    // Return unsubscribe function
    return () => {
      socket.off('connect');
      socket.off('disconnect');
      socket.off('connect_error');
      socket.off('message');
      socket.off('message_delivered');
    };
  });
}

// Saga to handle socket connection
function* connectSaga() {
  try {
    // Get connection details from state
    const serverUrl: string = yield select(selectServerUrl);
    const authToken: string = yield select(selectAuthToken);

    console.log('Connecting to socket server:', serverUrl);
    console.log('Auth token available:', !!authToken);

    // Create connection options
    const options: any = {};
    if (authToken) {
      options.auth = { token: authToken };
      console.log('Setting auth token in socket options');
    }

    // Close existing socket if any
    if (socket) {
      socket.disconnect();
    }

    // Connect to the server
    console.log('Attempting to connect to socket server...');
    socket = io(serverUrl, options);

    // Create a channel for socket events
    const socketChannel: EventChannel<any> = yield call(createSocketChannel, socket);

    // Process events from the channel
    while (true) {
      const event = yield take(socketChannel);

      // Handle different event types
      switch (event.type) {
        case 'connect':
          yield put(connectSuccess({ socketId: event.socketId }));
          console.log('✅ Connected as', event.socketId);

          // Emit client_ready event to signal that the client is ready to receive messages
          console.log('Emitting client_ready event');
          socket.emit('client_ready');
          break;
        case 'disconnect':
          console.log('❌ Disconnected');
          break;
        case 'error':
          yield put(connectFailure(event.error.message || 'Connection error'));
          console.error('Connection error:', event.error);
          break;
        case 'message_delivered':
          console.log('📬 Message delivery confirmation:', event.data);

          // Update message status to 'delivered' in the local database
          try {
            // Use client_message_id if available, otherwise fall back to message_id
            const messageId = event.data.client_message_id || event.data.message_id;

            if (messageId) {
              console.log('Updating message status to delivered using ID:', messageId);
              yield call(
                chatDBService.updateMessageStatus,
                messageId,
                'delivered'
              );
              console.log('✓✓ Message marked as delivered:', messageId);
            } else {
              console.log('No valid message ID found in delivery confirmation:', event.data);
            }
          } catch (error) {
            console.error('Failed to update message delivery status:', error);
          }
          break;
        case 'message':

          // Handle received message based on type
          try {
            const currentUser: string = yield select(selectUser);
            // Get sender username from data, fallback to sender_id for backward compatibility
            const senderUsername = event.data.sender_username;

            if (currentUser && senderUsername && event.data.message) {
              // Check message type
              const messageType = event.data.type || 'text';
              switch (messageType) {
                case 'clear_chat':
                  console.log('Received clear chat request from', senderUsername);
                  // Clear the chat in the local database and update room info
                  yield call(
                    chatDBService.clearRoom,
                    currentUser,
                    senderUsername
                  );

                  // Save the clear_chat message to database and update room info
                  yield call(
                    chatDBService.sendClearChatMessage,
                    senderUsername,
                    currentUser
                  );
                  break;

                case 'delete_user':
                  console.log('Received delete user request from', senderUsername);
                  // Delete the user room completely
                  yield call(
                    chatDBService.deleteUserRoom,
                    currentUser,
                    senderUsername
                  );

                  // Clear the current receiver to close the chat window
                  yield put(clearCurrentReceiver());
                  break;

                case 'typing':
                  // Handle typing indicator - update typing state in Redux
                  // Use the dedicated typing slice instead of messageReceived
                  yield put(setUserTyping({
                    userId: senderUsername, // This is actually a username
                    isTyping: event.data.message === 'typing'
                  }));
                  break;

                case 'text':
                default:

                if (__DEV__) {
                  console.log('Received text message:', event.data.message.substring(0, 20) + (event.data.message.length > 20 ? '...' : ''))
                }
                  // Regular text message - save to database and dispatch to UI
                  // This is a message from another user, so isMine=false
                  console.log(`Received message: ${event.data.id} from ${event.data.sender_username} to ${currentUser}`);
                  console.log(`Message content: ${event.data.message.substring(0, 30)}${event.data.message.length > 30 ? '...' : ''}`);

                  try {
                    // For received messages, we need to save them with the correct sender/receiver
                    // The receiver should be the current user (us)
                    const savedMessage = yield call(
                      chatDBService.saveMessage,
                      event.data.sender_username,
                      currentUser, // We are the receiver
                      event.data.message,
                      false, // Not our message
                      'text'
                    );
                    console.log('Successfully saved received message to local DB:', event.data.id);
                  } catch (error) {
                    console.error('Error saving received message to local DB:', error);
                  }
                  break;
              }
            }
          } catch (dbError) {
            console.error('Failed to handle received message:', dbError);
          }
          break;
        default:
          break;
      }
    }
  } catch (error: any) {
    console.error('Socket channel error:', error);
  } finally {
    if (yield cancelled()) {
      // Close the channel if the saga was cancelled
      // socketChannel.close();
    }
  }
}

// Saga to handle sending messages
function* sendMessageSaga(action: PayloadAction<{ receiverUsername: string, messageText: string, messageType?: 'text' | 'clear_chat' | 'typing' | 'delete_user' }>) {
  try {
    const { receiverUsername, messageText, messageType = 'text' } = action.payload;

    if (!socket) {
      throw new Error('Cannot send message: Not connected');
    }

    // Get current user (now this is a username, not a userId)
    const currentUser: string = yield select(selectUser);
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Send the message with type field and use acknowledgment callback
    console.log('Sending message with acknowledgment callback');

    // For text messages, we'll save the message first and then update its status when acknowledged
    let savedMessageId: string | null = null;

    if (messageType === 'text') {
      try {
        // Save message to local database with initial 'sending' status
        const savedMessage = yield call(
          chatDBService.saveMessage,
          currentUser,
          receiverUsername,
          messageText,
          true, // Our message
          'text'
        );
        savedMessageId = savedMessage.id;
        console.log('Saved message with ID:', savedMessageId);
      } catch (error) {
        console.error('Failed to save message before sending:', error);
      }
    }

    // Prepare message data
    const messageData = {
      receiver_username: receiverUsername,
      sender_username: currentUser,
      message: messageText,
      type: messageType,
      client_message_id: savedMessageId // Include the client's message ID
    };

    // For text messages, use acknowledgment callback
    if (messageType === 'text') {
      socket.emit('message', messageData, function(response: any) {
        // Handle acknowledgment from server
        console.log('Message acknowledgment received:', response);

        // Update message status to 'sent' when acknowledged
        if (savedMessageId && response && response.status === 'acknowledged') {
          chatDBService.updateMessageStatus(savedMessageId, 'sent')
            .then(() => console.log('Message status updated to sent'))
            .catch(err => console.error('Failed to update message status:', err));
        }
      });
    } else {
      // For non-text messages, don't use acknowledgment callback
      socket.emit('message', messageData);
    }

    // Handle different message types
    switch (messageType) {
      case 'clear_chat':
        console.log('Sending clear chat request to', receiverUsername);
        try {
          const currentUser: string = yield select(selectUser);
          if (currentUser) {
            // Clear local messages and update room info
            yield call(chatDBService.clearRoom, currentUser, receiverUsername);

            // Send a clear chat message (this will also update room info)
            yield call(
              chatDBService.sendClearChatMessage,
              currentUser,
              receiverUsername
            );
          }
        } catch (dbError) {
          console.error('Failed to clear chat:', dbError);
        }
        break;

      case 'delete_user':
        console.log('Sending delete user request to', receiverUsername);
        try {
          const currentUser: string = yield select(selectUser);
          if (currentUser) {
            // Delete the user room completely
            yield call(chatDBService.deleteUserRoom, currentUser, receiverUsername);

            // Clear the current receiver to close the chat window
            yield put(clearCurrentReceiver());
          }
        } catch (dbError) {
          console.error('Failed to delete user room:', dbError);
        }
        break;

      case 'typing':
        // Don't save typing indicators to database or add to messages list
        console.log('Sending typing indicator to', receiverUsername);
        // Update our own typing state for consistency
        const currentUser: string = yield select(selectUser);
        if (currentUser) {
          yield put(setUserTyping({
            userId: currentUser,
            isTyping: messageText === 'typing'
          }));
        }
        // No need to save to database or dispatch to messageReceived
        break;

      case 'text':
      default:
        // Add regular text message to our messages list
        // yield put(messageReceived(messageObj));

        // Save regular message to database
        try {
          const currentUser: string = yield select(selectUser);
          if (currentUser) {
            console.log(`Saving sent message from ${currentUser} to ${receiverUsername}: ${messageText}`);
            const savedMessage = yield call(
              chatDBService.saveMessage,
              currentUser,
              receiverUsername,
              messageText,
              true, // Our message
              'text'
            );
            console.log('Successfully saved sent message:', savedMessage.id);
          }
        } catch (dbError) {
          console.error('Failed to save message to database:', dbError);
        }
        break;
    }

    // Dispatch success action
    yield put(sendMessageSuccess());

  } catch (error: any) {
    // Handle send errors
    yield put(sendMessageFailure(error.message || 'Failed to send message'));
  }
}

// Saga to handle disconnection
function* disconnectSaga() {
  try {
    console.log('Disconnecting socket...');
    if (socket) {
      socket.disconnect();
      console.log('Socket disconnected successfully');
    } else {
      console.log('No active socket to disconnect');
    }
    yield put(disconnectSuccess());
  } catch (error: any) {
    console.error('Disconnect error:', error);
  }
}

// Root socket saga
export function* socketSaga() {
  // Handle socket actions
  yield takeEvery(connectRequest.type, connectSaga);
  yield takeEvery(sendMessageRequest.type, sendMessageSaga);
  yield takeEvery(disconnectRequest.type, disconnectSaga);
}

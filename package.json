{"name": "chatspot", "version": "1.0.0", "description": "ChatSpot Messenger - A full-stack chat application with web and mobile clients", "private": true, "workspaces": ["chatspot-backend", "chatspot-frontend", "chatspot-mobile", "chatspot-admin"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install-all": "npm install && cd chatspot-backend && npm install && cd ../chatspot-frontend && npm install && cd ../chatspot-mobile && npm install && cd ../chatspot-admin && npm install", "backend": "cd chatspot-backend && npm run start:dev", "backend:pg": "cd chatspot-backend && set USE_IN_MEMORY_DB=false && set DATABASE_URL=postgresql://chatspot_db_user:<EMAIL>/chatspot_db && set PORT=3001 && set NODE_ENV=development && npm run start:dev", "frontend": "cd chatspot-frontend && npm run dev", "admin": "cd chatspot-admin && npm run dev", "admin:prod": "cd chatspot-admin && npm run dev:prod", "mobile": "cd chatspot-mobile && npm start", "dev": "concurrently \"npm run backend\" \"npm run frontend\" \"npm run mobile\"", "dev:web": "concurrently \"npm run backend\" \"npm run frontend\"", "dev:admin": "concurrently \"npm run backend\" \"npm run admin\"", "dev:admin:prod": "concurrently \"npm run backend\" \"npm run admin:prod\"", "dev:pg": "concurrently \"npm run backend:pg\" \"npm run frontend\"", "dev:pg:admin": "concurrently \"npm run backend:pg\" \"npm run admin\"", "dev:pg:all": "concurrently \"npm run backend:pg\" \"npm run frontend\" \"npm run mobile\"", "test:backend": "cd chatspot-backend && npm test", "test:frontend": "cd chatspot-frontend && npm test", "test:mobile": "cd chatspot-mobile && npm test", "test:all": "concurrently \"npm run test:backend\" \"npm run test:frontend\" \"npm run test:mobile\"", "build:backend": "cd chatspot-backend && npm run build", "build:frontend": "cd chatspot-frontend && npm run build", "build:admin": "cd chatspot-admin && npm run build", "build:all": "npm run build:backend && npm run build:frontend && npm run build:admin", "deploy:admin": "cd chatspot-admin && npm run build && npx netlify deploy --prod --dir=dist", "clean": "rimraf node_modules && rimraf chatspot-backend/node_modules && rimraf chatspot-frontend/node_modules && rimraf chatspot-mobile/node_modules && rimraf chatspot-admin/node_modules", "create-admin": "cd chatspot-backend && npm run create-admin"}, "keywords": ["chat", "messenger", "react", "react-native", "<PERSON><PERSON><PERSON>", "monorepo"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}
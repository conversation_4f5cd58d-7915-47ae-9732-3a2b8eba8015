import React, { useState } from 'react';
import { TextInput, StyleSheet, TextInputProps } from 'react-native';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import Box from './Box';
import Text from './Text';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
}

export const Input = ({ 
  label, 
  error, 
  value, 
  onChangeText, 
  placeholder,
  secureTextEntry,
  ...rest 
}: InputProps) => {
  const theme = useTheme<Theme>();
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  const borderColor = error 
    ? theme.colors.error 
    : isFocused 
      ? theme.colors.primary 
      : theme.colors.border;

  return (
    <Box width="100%" marginBottom="m">
      {label && (
        <Text variant="caption" marginBottom="xs">
          {label}
        </Text>
      )}
      <TextInput
        style={[
          styles.input,
          {
            borderColor,
            color: theme.colors.text,
            backgroundColor: theme.colors.backgroundSecondary,
          },
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={theme.colors.textSecondary}
        secureTextEntry={secureTextEntry}
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...rest}
      />
      {error && (
        <Text variant="caption" color="error" marginTop="xs">
          {error}
        </Text>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
});

export default Input;

import { Entity, Column, PrimaryGeneratedC<PERSON>umn, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('messages')
export class Message {
  @ApiProperty({
    description: 'Unique identifier for the message',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Username of the sender',
    example: 'johndoe',
  })
  @Column()
  sender_username: string;

  @ApiProperty({
    description: 'Username of the receiver',
    example: 'janedoe',
  })
  @Column()
  receiver_username: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  @Column('text')
  message: string;

  @ApiProperty({
    description: 'Timestamp when the message was sent',
    example: '2023-10-15T14:30:00Z',
  })
  @CreateDateColumn()
  timestamp: Date;

  @ApiProperty({
    description: 'Status of the message',
    example: 'delivered',
    enum: ['pending', 'delivered'],
  })
  @Column({ default: 'pending' })
  status: 'pending' | 'delivered';

  @ApiProperty({
    description: 'Timestamp when the message was delivered',
    example: '2023-10-15T14:30:05Z',
    nullable: true,
  })
  @Column({ type: 'timestamp', nullable: true })
  delivered_at: Date | null;

  @ApiProperty({
    description: 'Message type',
    example: 'text',
    enum: ['text', 'clear_chat', 'typing', 'delete_user', 'system', 'image', 'video', 'file'],
  })
  @Column({ default: 'text' })
  type: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system' | 'image' | 'video' | 'file';

  @ApiProperty({
    description: 'File URL for media messages',
    example: '/uploads/image-123456789.jpg',
    nullable: true,
  })
  @Column({ nullable: true })
  file_url: string | null;

  @ApiProperty({
    description: 'Original file name',
    example: 'vacation-photo.jpg',
    nullable: true,
  })
  @Column({ nullable: true })
  file_name: string | null;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
    nullable: true,
  })
  @Column({ nullable: true })
  file_size: number | null;

  @ApiProperty({
    description: 'File MIME type',
    example: 'image/jpeg',
    nullable: true,
  })
  @Column({ nullable: true })
  file_type: string | null;

  @ApiProperty({
    description: 'Thumbnail URL for media files',
    example: '/uploads/thumb-image-123456789.jpg',
    nullable: true,
  })
  @Column({ nullable: true })
  thumbnail_url: string | null;
}
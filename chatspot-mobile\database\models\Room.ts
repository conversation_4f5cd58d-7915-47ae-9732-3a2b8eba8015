import { Model } from '@nozbe/watermelondb';
import { field, date, text } from '@nozbe/watermelondb/decorators';

export class Room extends Model {
  static table = 'rooms';

  @text('room_id') roomId!: string;
  @text('username') username!: string;
  @text('other_username') otherUsername!: string;
  @text('last_message') lastMessage!: string;
  @date('updated') updated!: number;
  @field('unread_count') unreadCount!: number;

  // Add toJSON method for serialization
  toJSON() {
    return {
      id: this.id,
      room_id: this.roomId,
      roomId: this.roomId,
      username: this.username,
      other_username: this.otherUsername,
      otherUsername: this.otherUsername,
      last_message: this.lastMessage,
      lastMessage: this.lastMessage,
      updated: this.updated,
      unread_count: this.unreadCount,
      unreadCount: this.unreadCount
    };
  }
}

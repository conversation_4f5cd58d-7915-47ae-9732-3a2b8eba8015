import React from 'react';
import { TouchableOpacity, ActivityIndicator, StyleSheet, Text, View } from 'react-native';

interface ButtonProps {
  label: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
}

export const Button = ({
  label,
  onPress,
  variant = 'primary',
  loading = false,
  disabled = false,
  fullWidth = false,
}: ButtonProps) => {
  // Define colors directly without using theme
  const primaryColor = '#0a7ea4';
  const secondaryColor = '#E5E7EB';
  const disabledColor = '#9CA3AF';

  const backgroundColor =
    disabled ? disabledColor :
    variant === 'primary' ? primaryColor : secondaryColor;

  const textColor =
    disabled ? '#6B7280' :
    variant === 'primary' ? '#FFFFFF' : '#111827';

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[
        styles.button,
        { backgroundColor },
        fullWidth && styles.fullWidth,
      ]}
    >
      <View style={styles.contentContainer}>
        {loading && (
          <ActivityIndicator
            size="small"
            color={textColor}
            style={styles.loader}
          />
        )}
        <Text style={[styles.buttonText, { color: textColor }]}>
          {label}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullWidth: {
    width: '100%',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loader: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Button;

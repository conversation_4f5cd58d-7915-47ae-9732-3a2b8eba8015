import { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { selectIsAuthenticated, selectAuthToken, selectUser, logout } from '../redux/slices/authSlice';
import { connectRequest, disconnectRequest } from '../redux/slices/socketSlice';
import { setCurrentRoom, setCurrentReceiver, clearCurrentReceiver, selectDBInitialized, selectCurrentReceiverUsername, initializeDatabase, setInitialized } from '../redux/slices/chatDBSlice';
import { chatDBService } from '../database/service';
import { useWatermelonObservable } from '../hooks/useWatermelonObservable';
import ChatWindow from './ChatWindow';
import RoomsList from './RoomsList';
import NewChatModal from './NewChatModal.tsx';
import Settings from './Settings.tsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'; // <-- This is important!
import { faCoffee } from '@fortawesome/free-solid-svg-icons';  // <-- Import the icons you need
import { faGear } from '@fortawesome/free-solid-svg-icons';
import Profile from './Profile';


import './Chat.css';
import { getRoomId } from '../database/config';


const Chat: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const isAuthenticated = useSelector(selectIsAuthenticated);
  const currentUser = useSelector(selectUser);
  const dbInitialized = useSelector(selectDBInitialized);
  const selectedReceiverUsername = useSelector(selectCurrentReceiverUsername);
  const [showProfile, setShowProfile] = useState(false);

  // Use WatermelonDB observables
  const rooms = useWatermelonObservable(
    currentUser ? chatDBService.observeRooms(currentUser) : null,
    []
  );

  // Get messages from the database
  const messages = useWatermelonObservable(
    (currentUser && selectedReceiverUsername) ?
      chatDBService.observeMessages(currentUser, selectedReceiverUsername) :
      null,
    []
  );


  const [showNewChatModal, setShowNewChatModal] = useState<boolean>(false);
  const [isMobileView, setIsMobileView] = useState<boolean>(window.innerWidth < 768);

  const roomsContainerRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showSettings, setShowSettings] = useState<boolean>(false);


  // Handle window resize to detect mobile/desktop view
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobileView(mobile);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);


  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  // Initialize database and connect to socket
  useEffect(() => {
    const init = async () => {
      if (!dbInitialized) {
        const result = await initializeDatabase();
        dispatch(setInitialized(result));
      }
    };
    init();
  }, [dbInitialized, dispatch]);

  // Redirect to login if not authenticated
  useEffect(() => {
    console.log('isAuthenticated', isAuthenticated);
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Update room ID when receiver changes and mark messages as read
  useEffect(() => {
    if (currentUser && selectedReceiverUsername) {
      const roomId = getRoomId(currentUser, selectedReceiverUsername);
      console.log('roomId', roomId);
      dispatch(setCurrentRoom(roomId));

      // Mark messages as read when chat is opened
      chatDBService.markMessagesAsRead(currentUser, selectedReceiverUsername)
        .catch(error => console.error('Error marking messages as read:', error));
    }
  }, [currentUser, selectedReceiverUsername, dispatch]);

  const handleRoomSelect = (username: string) => {
    dispatch(setCurrentReceiver(username));

    // Mark messages as read when a room is selected
    if (currentUser) {
      chatDBService.markMessagesAsRead(currentUser, username)
        .catch(error => console.error('Error marking messages as read:', error));
    }

    // On mobile, navigate to dedicated chat room screen
    if (isMobileView) {
      navigate(`/chat/${username}`);
    } else {
      // On desktop, close settings panel if it's open
      if (showSettings) {
        setShowSettings(false);
      }
      // Also close profile view if it's open
      if (showProfile) {
        setShowProfile(false);
      }
    }
  };

  const handleNewChat = () => {
    setShowNewChatModal(true);
  };

  const handleCloseModal = () => {
    setShowNewChatModal(false);
  };

  const handleSettings = () => {
    if (isMobileView) {
      // On mobile, navigate to the dedicated settings page
      navigate('/settings');
    } else {
      // On desktop, show settings in the current view
      setShowSettings(true);
      dispatch(clearCurrentReceiver());
    }
  };

  // Handle showing profile
  const handleShowProfile = (username: string) => {
    if (isMobileView) {
      // On mobile, navigate to the dedicated profile page
      navigate(`/profile/${username}`);
    } else {
      // On desktop, show profile in the current view
      setShowProfile(true);
    }
  };

  const handleCloseSettings = () => {
    setShowSettings(false);
  };


  if (showSettings) {
    { console.log("showsetting") }
  }


  const handleStartChat = (username: string) => {
    // Prevent chatting with yourself
    if (username !== currentUser) {
      dispatch(setCurrentReceiver(username));
      setShowNewChatModal(false);

      // Mark messages as read when a new chat is started
      if (currentUser) {
        chatDBService.markMessagesAsRead(currentUser, username)
          .catch(error => console.error('Error marking messages as read:', error));
      }

      // On mobile, navigate to dedicated chat room screen
      if (isMobileView) {
        navigate(`/chat/${username}`);
      } else {
        // On desktop, close settings panel if it's open
        if (showSettings) {
          setShowSettings(false);
        }
        // Also close profile view if it's open
        if (showProfile) {
          setShowProfile(false);
        }
      }
    }
  };

  const getAvatarText = () => {
    return currentUser?.charAt(0).toUpperCase();
  };

  return (
    <div className="chat-container">
      <div className="chat-content">
        {/* Mobile View - Only show rooms list */}
        {isMobileView ? (
          <>
            {/* Mobile Header */}
            <div className="mobile-header">
              <div className="mobile-rooms-header">
                <div className="header-actions" onClick={handleSettings} title="Open Profile Settings">
                  <div className="user-avatar user-avatar-self">
                    {getAvatarText()}
                  </div>
                  <span className="current-user">{currentUser}</span>
                  <div className="user-setting"><FontAwesomeIcon icon={faGear} className="setting-button" /></div>
                </div>
                <div className="header-buttons">
                  <button className="add-button" onClick={handleNewChat}>
                    +
                  </button>
                </div>
              </div>
            </div>

            {/* Rooms List */}
            <div className="rooms-container" ref={roomsContainerRef}>
              <div className="rooms-header">
                <div className="tab-header">CHATS</div>
              </div>
              <RoomsList
                rooms={rooms}
                onRoomSelect={handleRoomSelect}
                selectedUsername={selectedReceiverUsername}
              />
            </div>

            {/* Settings - Shown when settings are open */}
            {showSettings && (
              <div className="chat-window-container">
                <Settings
                  onClose={handleCloseSettings}
                  onLogout={handleLogout}
                />
              </div>
            )}
          </>
        ) : (
          /* Desktop View */
          <>
            {/* Rooms container */}
            <div className="rooms-container" ref={roomsContainerRef}>
              <div className={`profile-header ${showSettings ? ' setting-selected' : ''}`}>
                <div className="header-actions"  onClick={handleSettings} title="Open Profile Settings">
                  <div className="user-avatar user-avatar-self">
                    {getAvatarText()}
                  </div>
                  <span className="current-user">{currentUser}</span>
                  <div className="user-setting"><FontAwesomeIcon icon={faGear} size="xl" color="#494040" className="setting-button" /></div>
                </div>
                <div className="header-buttons">
                  <button className="logout-button" onClick={handleNewChat}>
                    New Chat
                  </button>
                </div>
              </div>
              <div className="rooms-header">
                <div className="tab-header">CHATS</div>
              </div>
              <RoomsList
                rooms={rooms}
                onRoomSelect={handleRoomSelect}
                selectedUsername={selectedReceiverUsername}
              />
            </div>

            {/* Messages section */}
            <div className="messages-section" ref={messagesContainerRef}>
             {showProfile ? (
  <div className="chat-window-container">
    <Profile
      username={selectedReceiverUsername || ''}
      onClose={() => setShowProfile(false)} // Close profile, go back to chat
      onLogout={handleLogout}
    />
  </div>
) : selectedReceiverUsername ? (
  <div className="chat-window-container">
    <ChatWindow
      messages={messages}
      receiverUsername={selectedReceiverUsername}
      onClearChat={() => console.log('Chat cleared')}
      onShowProfile={() => handleShowProfile(selectedReceiverUsername)} // Use the new handler
    />
  </div>
)  : showSettings ? (
                <div className="chat-window-container">
                  <Settings
                    onClose={handleCloseSettings}
                    onLogout={handleLogout}
                  />
                </div>
              ) : (
                <div className="no-chat-selected">
                  <div className="no-chat-content">
                    <div className="no-chat-icon">
                      <img src="src/icons/comments-alt.png" style={{ opacity: 0.4 }} />
                    </div>
                    <h3>Select a chat or start a new conversation</h3>
                    <p>Choose a contact from the list or start a new chat by clicking the "New Chat" button.</p>
                    <button className="start-chat-button" onClick={handleNewChat}>Start New Chat</button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {showNewChatModal && (
        <NewChatModal onClose={handleCloseModal} onStartChat={handleStartChat} />
      )}
    </div>
  );
};

export default Chat;

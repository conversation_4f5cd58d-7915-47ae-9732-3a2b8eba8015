import { Controller, Get, Param, NotFoundException, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { UserDto } from './dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from './jwt-auth.guard';

@ApiTags('users')
@Controller('api/users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('resolve/:username')
  @ApiOperation({ summary: 'Resolve username to userId' })
  @ApiParam({ name: 'username', description: 'Username to resolve', example: 'johndoe' })
  @ApiResponse({
    status: 200,
    description: 'Returns the userId for the given username',
    schema: {
      type: 'object',
      properties: {
        userId: {
          type: 'string',
          example: '123e4567-e89b-12d3-a456-426614174000',
          description: 'The user ID corresponding to the username',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async resolveUsername(@Param('username') username: string): Promise<{ userId: string }> {
    try {
      const userId = await this.usersService.findUserIdByUsername(username);
      return { userId };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('Error resolving username');
    }
  }

  @Get(':userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get user information by userId' })
  @ApiParam({ name: 'userId', description: 'User ID to fetch', example: '123e4567-e89b-12d3-a456-426614174000' })
  @ApiResponse({
    status: 200,
    description: 'Returns the user information',
    type: UserDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('userId') userId: string): Promise<UserDto> {
    return this.usersService.findUserById(userId);
  }
}

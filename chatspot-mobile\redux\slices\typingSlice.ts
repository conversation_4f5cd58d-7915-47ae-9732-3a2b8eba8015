import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

interface TypingState {
  isUserTyping: boolean;
  typingUsers: Record<string, boolean>;
}

const initialState: TypingState = {
  isUserTyping: false,
  typingUsers: {}
};

const typingSlice = createSlice({
  name: 'typing',
  initialState,
  reducers: {
    setUserTyping: (state, action: PayloadAction<{ userId: string; isTyping: boolean }>) => {
      // Note: userId is actually a username
      const { userId, isTyping } = action.payload;

      if (isTyping) {
        state.typingUsers[userId] = true;
      } else {
        delete state.typingUsers[userId];
      }
    },
    setIsTyping: (state, action: PayloadAction<boolean>) => {
      state.isUserTyping = action.payload;
    },
    clearTypingUsers: (state) => {
      state.typingUsers = {};
    }
  }
});

export const { setUserTyping, setIsTyping, clearTypingUsers } = typingSlice.actions;

export const selectIsUserTyping = (state: RootState) => state.typing.isUserTyping;
export const selectTypingUsers = (state: RootState) => state.typing.typingUsers;

export default typingSlice.reducer;

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcrypt');
require('dotenv').config();

async function createAdminUser() {
  // Get database connection details from environment variables
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: databaseUrl,
    ssl: { rejectUnauthorized: false }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Check if admin user exists
    const checkResult = await client.query('SELECT EXISTS(SELECT 1 FROM users WHERE is_admin = true)');
    const adminExists = checkResult.rows[0].exists;

    if (adminExists) {
      console.log('Admin user already exists. No new admin created.');
    } else {
      // Create admin user with password Admin@123
      const password = 'Admin@123';
      const hashedPassword = await bcrypt.hash(password, 10);
      
      await client.query(
        'INSERT INTO users (username, password, is_admin) VALUES ($1, $2, $3)',
        ['admin', hashedPassword, true]
      );
      
      console.log('Admin user created successfully!');
      console.log('Username: admin');
      console.log('Password: Admin@123');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await client.end();
  }
}

createAdminUser();

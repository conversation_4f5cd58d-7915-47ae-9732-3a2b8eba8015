.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-background);
  border-radius: 25px;
  width: 95%;
  max-width: 350px;
  box-shadow: var(--shadow-lg);
  animation: modalFadeIn 0.3s ease-out;
}

@media (min-width: 768px) {
  .modal-content {
    width: 90%;
    max-width: 450px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 16px;
}

@media (min-width: 768px) {
  .modal-header {
    padding: 16px 20px;
  }
}

.modal-header h3 {
  margin: 0;
  color: #FF4500; /* Updated primary color */
  font-size: 16px;
  font-weight: 600;
}

@media (min-width: 768px) {
  .modal-header h3 {
    font-size: 18px;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 22px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  min-height: auto;
}

@media (min-width: 768px) {
  .close-button {
    font-size: 24px;
  }
}

.close-button:hover {
  color: var(--text-color);
}

.modal-content form {
  padding: 0px 20px;
}



.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #888;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 45px;
  font-size: 17px;
}

.form-group input:focus {
  border-color: #FF4500; /* Updated primary color */
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.2); /* Updated primary color */
}

.form-group input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.error-message {
  color: #e53935;
  font-size: 14px;
  margin-top: 5px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin: 20px 0px;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: none;
  padding: 10px 16px;
  border-radius: 45px;
  cursor: pointer;
  font-weight: 500;
  height: 50px;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.start-chat-button {
  background-color: #FF4500; /* Updated primary color */
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(255, 69, 0, 0.3); /* Updated primary color */
  transition: all 0.2s;
}

.start-chat-button:hover {
  background-color: #E03A2A;
  box-shadow: 0 3px 6px rgba(255, 69, 0, 0.4); /* Updated primary color */
}

.start-chat-button:disabled {
  background-color: #f0a39a;
  cursor: not-allowed;
  box-shadow: none;
}

.start-chat-button:disabled:hover {
  background-color: #f0a39a;
  box-shadow: none;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-room-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: var(--card-background);
  overflow: hidden;
}

.chat-room-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--primary-color-light);
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.chat-room-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--primary-color);
  margin-left: 10px;
  flex: 1;
  text-align: center;
}

.back-button {
  background-color: transparent;
  border: none;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  min-height: 40px;
  cursor: pointer;
  font-size: 0.9rem;
}

.back-button:hover {
  background-color: rgba(240, 79, 61, 0.1);
  border-radius: 4px;
}

.back-button-icon {
  font-size: 18px;
}

.chat-room-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-window-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

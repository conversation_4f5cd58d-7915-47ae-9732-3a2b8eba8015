# Firebase Cloud Messaging Setup for Chatspot Backend

This guide explains how to set up Firebase Cloud Messaging for push notifications in the Chatspot backend.

## Prerequisites

1. A Google account
2. A Firebase project (the same one used for the frontend)

## Step 1: Generate a Service Account Key

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (guptmessenger-14966)
3. Go to Project Settings > Service accounts
4. Click "Generate new private key"
5. Save the JSON file securely

## Step 2: Configure Environment Variables

1. Open your `.env` file
2. Add the following environment variables:

```
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Private Key\n-----END PRIVATE KEY-----\n"
```

Note: Make sure to replace newlines in the private key with `\n` and wrap the entire key in double quotes.

## Step 3: Test the Notification Service

You can test the notification service by sending a test notification:

1. Make sure your backend is running
2. Register an FCM token using the frontend
3. Send a test notification using the API endpoint:

```
POST /api/notifications/test/:username
```

## Troubleshooting

- **Firebase initialization error**: Check that your environment variables are set correctly
- **Failed to send notification**: Check that the FCM token is valid and the user exists
- **Permission denied**: Make sure your service account has the necessary permissions

## Security Considerations

- Keep your service account key secure and never commit it to version control
- Use environment variables to store sensitive information
- Consider using Firebase Security Rules to restrict access to your Firebase resources

## Resources

- [Firebase Admin SDK Documentation](https://firebase.google.com/docs/admin/setup)
- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Firebase Service Account Setup](https://firebase.google.com/docs/admin/setup#initialize-sdk)

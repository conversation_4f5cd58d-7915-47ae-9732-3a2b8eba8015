import { database, getRoomId } from './config';
import { Chat, MessageType } from './models/Chat';
import { Room } from './models/Room';
import { Q } from '@nozbe/watermelondb';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { initializeSQLite } from './sqlite-helper';

// Get collection references
const chatsCollection = database.get<Chat>('chats');
const roomsCollection = database.get<Room>('rooms');

// Database service for chat operations
export const chatDBService = {
  // Initialize the database
  initialize: async (): Promise<boolean> => {
    try {
      // Initialize SQLite first
      await initializeSQLite();

      // Perform a simple write operation to ensure the database is working
      await database.write(async () => {
        // Just check if we can access the collections
        const chats = await chatsCollection.query().fetch();
        const rooms = await roomsCollection.query().fetch();
        console.log('Database initialized with', chats.length, 'chats and', rooms.length, 'rooms');
      });
      return true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      return false;
    }
  },

  // Save a new message with type
  saveMessage: async (
    sender: string,
    receiver: string,
    message: string,
    isMine: boolean = true,
    type: MessageType = 'text' // Default type is text
  ): Promise<Chat> => {
    try {
      const roomId = getRoomId(sender, receiver);
      const timestamp = Date.now();

      // Use database.write to perform all operations in a single transaction
      const chatMessage = await database.write(async () => {
        // Create the message
        const chatMessage = await chatsCollection.create(chat => {
          chat.roomId = roomId;
          chat.senderUsername = sender;
          chat.receiverUsername = receiver;
          chat.message = message;
          chat.type = type;
          chat.timestamp = timestamp;
          chat.status = isMine ? 'sending' : 'sent'; // Set initial status to 'sending' for outgoing messages
          chat.isMine = isMine;
        });

        // Update or create the room for the sender
        const existingRoom = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', sender)
        ).fetch();

        console.log('existingRoom.length', chatMessage)

        if (existingRoom.length > 0) {
          // Update existing room
          await existingRoom[0].update(room => {
            room.lastMessage = message;
            room.updated = timestamp;
            // Update usernames if they're not set
            if (!room.username) {
              room.username = sender;
            }
            if (!room.otherUsername) {
              room.otherUsername = receiver;
            }
            // Only increment unread count if the message is from the other user
            if (!isMine) {
              room.unreadCount += 1;
            }
          });
        } else {
          // Create new room for the sender
          await roomsCollection.create(room => {
            room.roomId = roomId;
            room.username = sender;
            room.otherUsername = receiver;
            room.lastMessage = message;
            room.updated = timestamp;
            room.unreadCount = isMine ? 0 : 1;
          });
        }

        // Always update the receiver's room, regardless of who sent the message
        // This ensures rooms are updated for both sent and received messages
        // Update or create the room for the receiver
        const existingReceiverRoom = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', receiver)
        ).fetch();

        if (existingReceiverRoom.length > 0) {
          // Update existing room
          await existingReceiverRoom[0].update(room => {
            room.lastMessage = message;
            room.updated = timestamp;
            // Update usernames if they're not set
            if (!room.username) {
              room.username = receiver;
            }
            if (!room.otherUsername) {
              room.otherUsername = sender;
            }
            // Increment unread count for the receiver
            room.unreadCount += 1;
          });
        } else {
          // Create new room for the receiver
          await roomsCollection.create(room => {
            room.roomId = roomId;
            room.username = receiver;
            room.otherUsername = sender;
            room.lastMessage = message;
            room.updated = timestamp;
            room.unreadCount = 1; // New message for receiver
          });
        }

        return chatMessage;
      });

      return chatMessage;
    } catch (error) {
      console.error('Failed to save message:', error);
      throw error;
    }
  },

  // Clear chat history for a room
  clearChat: async (userId: string, otherUserId: string): Promise<void> => {
    try {
      const roomId = getRoomId(userId, otherUserId);

      await database.write(async () => {
        // Delete all messages in the room
        const messages = await chatsCollection.query(
          Q.where('room_id', roomId)
        ).fetch();

        for (const message of messages) {
          await message.destroyPermanently();
        }

        // Update the room
        const rooms = await roomsCollection.query(
          Q.where('room_id', roomId)
        ).fetch();

        for (const room of rooms) {
          await room.update(r => {
            r.lastMessage = '';
            r.updated = Date.now();
            r.unreadCount = 0;
          });
        }
      });
    } catch (error) {
      console.error('Failed to clear chat:', error);
      throw error;
    }
  },

  // Mark messages as read
  markMessagesAsRead: async (userId: string, otherUserId: string): Promise<void> => {
    try {
      const roomId = getRoomId(userId, otherUserId);

      await database.write(async () => {
        // Update the room to reset unread count
        const rooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', userId)
        ).fetch();

        for (const room of rooms) {
          await room.update(r => {
            r.unreadCount = 0;
          });
        }

        // Update messages to mark as read
        const messages = await chatsCollection.query(
          Q.where('room_id', roomId),
          Q.where('receiver_username', userId),
          Q.where('status', Q.notEq('read'))
        ).fetch();

        for (const message of messages) {
          await message.update(m => {
            m.status = 'read';
          });
        }
      });
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
      throw error;
    }
  },

  // Get messages for a room
  getMessages: async (userId1: string, userId2: string): Promise<Record<string, any>[]> => {
    try {
      const roomId = getRoomId(userId1, userId2);

      const messages = await chatsCollection.query(
        Q.where('room_id', roomId),
        Q.sortBy('timestamp', Q.asc)
      ).fetch();

      return messages.map(message => message?.toJSON());
    } catch (error) {
      console.error('Failed to get messages:', error);
      return [];
    }
  },

  // Observe messages for a room (reactive) - returns JSON objects
  observeMessages: (userId1: string, userId2: string): Observable<Record<string, any>[]> => {
    const roomId = getRoomId(userId1, userId2);
    console.log(`Observing messages for room ${roomId} (users: ${userId1}, ${userId2})`);

    return chatsCollection.query(
      Q.where('room_id', roomId),
      Q.sortBy('timestamp', Q.asc)
    ).observe()
    .pipe(
      map(messages => {
        console.log(`Observable received ${messages.length} messages for room ${roomId}`);
        if (messages.length > 0) {
          console.log(`First message: ${messages[0].message}, isMine: ${messages[0].isMine}`);
        }
        return messages.map(message => {
          try {
            // Try to use toJSON if available
            if (message && typeof message?.toJSON === 'function') {
              return message?.toJSON();
            }

            // Fallback to manual conversion
            return {
              id: message.id,
              message: message.message,
              timestamp: message.timestamp,
              is_mine: message.isMine,
              status: message.status,
              sender_username: message.senderUsername,
              receiver_username: message.receiverUsername,
              type: message.type,
              room_id: message.roomId
            };
          } catch (error) {
            console.error('Error converting message to JSON:', error, message);
            // Return a minimal valid message object
            return {
              id: message?.id || `fallback_${Date.now()}`,
              message: message?.message || '',
              timestamp: message?.timestamp || Date.now(),
              is_mine: message?.isMine || false,
              status: message?.status || 'sent'
            };
          }
        });
      })
    );
  },

  // Observe message records for a room (reactive) - returns raw records for individual reactivity
  observeMessageRecords: (userId1: string, userId2: string): Observable<Chat[]> => {
    const roomId = getRoomId(userId1, userId2);
    console.log(`Observing message records for room ${roomId} (users: ${userId1}, ${userId2})`);

    return chatsCollection.query(
      Q.where('room_id', roomId),
      Q.sortBy('timestamp', Q.asc)
    ).observe();
  },

  // Get all rooms for a user
  getRooms: async (username: string): Promise<Record<string, any>[]> => {
    try {
      // Only return rooms where the user is the owner
      const rooms = await roomsCollection.query(
        Q.where('username', username),
        Q.sortBy('updated', Q.desc)
      ).fetch();

      console.log(`Found ${rooms.length} rooms for user ${username}`);
      return rooms.map(room => room.toJSON());
    } catch (error) {
      console.error('Failed to get rooms:', error);
      return [];
    }
  },

  // Observe rooms for a user (reactive)
  observeRooms: (username: string): Observable<Record<string, any>[]> => {
    // Only return rooms where the user is the owner
    return roomsCollection.query(
      Q.where('username', username),
      Q.sortBy('updated', Q.desc)
    ).observe()
    .pipe(
      map(rooms => {
        console.log(`Found ${rooms.length} rooms for user ${username}`);
        return rooms.map(room => room.toJSON());
      })
    );
  },

  // Get total unread message count
  getUnreadCount: async (username: string): Promise<number> => {
    try {
      const rooms = await roomsCollection.query(
        Q.where('username', username)
      ).fetch();

      return rooms.reduce((total, room) => total + room.unreadCount, 0);
    } catch (error) {
      console.error('Failed to get unread count:', error);
      return 0;
    }
  },

  // Clear all messages in a room
  clearRoom: async (userId: string, otherUserId: string): Promise<void> => {
    try {
      const roomId = getRoomId(userId, otherUserId);

      // Delete all messages in the room
      return database.write(async () => {
        // Find all messages in the room
        const messages = await chatsCollection.query(
          Q.where('room_id', roomId)
        ).fetch();

        // Delete each message
        for (const message of messages) {
          await message.destroyPermanently();
        }

        // Update the room's last message
        const existingRoom = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', userId)
        ).fetch();

        if (existingRoom.length > 0) {
          await existingRoom[0].update(room => {
            room.lastMessage = 'Chat cleared';
            room.updated = Date.now();
            room.unreadCount = 0;
          });
        }
      });
    } catch (error) {
      console.error('Failed to clear room:', error);
      throw error;
    }
  },

  // Send a clear chat message
  sendClearChatMessage: async (sender: string, receiver: string): Promise<Chat> => {
    try {
      const roomId = getRoomId(sender, receiver);
      const timestamp = Date.now();
      const message = 'Chat cleared';

      return database.write(async () => {
        // Create a system message indicating chat was cleared
        const chatMessage = await chatsCollection.create(chat => {
          chat.roomId = roomId;
          chat.senderUsername = sender;
          chat.receiverUsername = receiver;
          chat.message = message;
          chat.timestamp = timestamp;
          chat.type = 'system';
          chat.status = 'sent';
          chat.isMine = true;
        });

        // Update the room for the sender
        const existingRoom = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', sender)
        ).fetch();

        if (existingRoom.length > 0) {
          await existingRoom[0].update(room => {
            room.lastMessage = message;
            room.updated = timestamp;
            room.unreadCount = 0;
            // Update usernames if they're not set
            if (!room.username) {
              room.username = sender;
            }
            if (!room.otherUsername) {
              room.otherUsername = receiver;
            }
          });
        }

        return chatMessage;
      });
    } catch (error) {
      console.error('Failed to send clear chat message:', error);
      throw error;
    }
  },

  // Delete a user room completely
  deleteUserRoom: async (userId: string, otherUserId: string): Promise<void> => {
    try {
      const roomId = getRoomId(userId, otherUserId);

      return database.write(async () => {
        // Delete all messages in the room
        const messages = await chatsCollection.query(
          Q.where('room_id', roomId)
        ).fetch();

        for (const message of messages) {
          await message.destroyPermanently();
        }

        // Delete the room
        const rooms = await roomsCollection.query(
          Q.where('room_id', roomId),
          Q.where('username', userId)
        ).fetch();

        for (const room of rooms) {
          await room.destroyPermanently();
        }
      });
    } catch (error) {
      console.error('Failed to delete user room:', error);
      throw error;
    }
  },

  // Update message status
  updateMessageStatus: async (messageId: string, status: string): Promise<boolean> => {
    try {
      const message = await chatsCollection.find(messageId);

      await database.write(async () => {
        await message.update(msg => {
          msg.status = status;
        });
      });

      return true;
    } catch (error) {
      console.error('Failed to update message status:', error);
      return false;
    }
  }
};

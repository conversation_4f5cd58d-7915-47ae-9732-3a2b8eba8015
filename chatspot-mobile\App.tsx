import React, { useEffect, useState } from 'react';
import { StatusBar } from 'react-native';
import { Provider } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as SplashScreen from 'expo-splash-screen';
import { useFonts } from 'expo-font';
import { ThemeProvider } from './theme/ThemeProvider';
import AppNavigator from './navigation/AppNavigator';
import store from './redux/store';
import { chatDBService } from './database/service';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

export default function App() {
  // Load fonts
  const [fontsLoaded] = useFonts({
    'SpaceMono': require('./assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Track database initialization (used for debugging purposes)
  const [dbInitialized, setDbInitialized] = useState(false);
  console.log('Database initialized:', dbInitialized);

  // Initialize database and hide splash screen when fonts are loaded
  useEffect(() => {
    async function initializeApp() {
      if (fontsLoaded) {
        try {
          // Initialize the database
          const success = await chatDBService.initialize();
          setDbInitialized(success);
          console.log('Database initialization:', success ? 'successful' : 'failed');
        } catch (error) {
          console.error('Error initializing database:', error);
          setDbInitialized(false);
        } finally {
          // Hide splash screen regardless of database initialization result
          await SplashScreen.hideAsync();
        }
      }
    }

    initializeApp();
  }, [fontsLoaded]);

  // Don't render until fonts are loaded
  if (!fontsLoaded) {
    return null;
  }

  return (
    <Provider store={store}>
      <ThemeProvider>
        <SafeAreaProvider>
          <StatusBar barStyle="light-content" />
          <AppNavigator />
        </SafeAreaProvider>
      </ThemeProvider>
    </Provider>
  );
}

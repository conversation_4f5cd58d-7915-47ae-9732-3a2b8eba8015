import { Model } from '@nozbe/watermelondb';
import { field, date, text, readonly } from '@nozbe/watermelondb/decorators';

// Define message types
export type MessageType = 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system';

export class Chat extends Model {
  static table = 'chats';

  @text('room_id') roomId!: string;
  @text('sender_username') senderUsername!: string;
  @text('receiver_username') receiverUsername!: string;
  @text('message') message!: string;
  @text('type') type!: MessageType;
  @date('timestamp') timestamp!: number;
  @text('status') status!: string;
  @field('is_mine') isMine!: boolean;

  // Add toJSON method for serialization
  toJSON() {
    return {
      id: this.id,
      room_id: this.roomId,
      roomId: this.roomId,
      sender_username: this.senderUsername,
      senderUsername: this.senderUsername,
      receiver_username: this.receiverUsername,
      receiverUsername: this.receiverUsername,
      message: this.message,
      type: this.type,
      timestamp: this.timestamp,
      status: this.status,
      is_mine: this.isMine,
      isMine: this.isMine
    };
  }

  // Helper methods
  getFormattedTime(): string {
    return new Date(this.timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}

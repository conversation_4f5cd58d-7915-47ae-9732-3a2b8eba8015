.message-input-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* Ensure emoji bar is properly positioned on mobile */
@media (max-width: 767px) {
  .message-input-wrapper {
    padding-bottom: 4px;
  }
}

.message-input-form {
  display: flex;
  width: 100%;
  position: relative;
}

.message-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 16px;
  border-radius: 0; /* No border radius on mobile */
  flex-wrap: nowrap;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: var(--card-background);
  border-top: 1px solid var(--border-color);
  box-sizing: border-box;
}

@media (min-width: 768px) {
  .message-input-container {
    padding: 12px 16px;
    position: relative;
    border-top: none;
    left: auto;
    right: auto;
    width: auto;
  }
}

.message-input {
  border: 1px solid #ddd;
  border-radius: 45px;
  padding: 12px 16px;
  font-size: 17px;
  line-height: 20px;
  min-height: 44px;
  max-height: 120px;
  resize: none;
  outline: none;
  font-family: inherit;
  min-width: 0; /* Allows flex item to shrink below content size */
  color: var(--text-color);
  transition: border-color 0.2s, box-shadow 0.2s;
  width: calc(100% - 110px); /* Account for send button + attachment button width */
  display: block;
  box-sizing: border-box;
  overflow: hidden;
  margin-left: 48px; /* Space for attachment button */
}

@media (min-width: 768px) {
  .message-input {
    border-radius: 22px;
    padding: 12px 16px;
    font-size: 15px;
    min-height: 46px;
    width: calc(100% - 108px); /* Account for both buttons on desktop */
    margin-left: 50px;
  }
}

.message-input:focus {
  border-color: var(--primary-color);
  border-width: 2px;
  box-shadow: 0 0 0 2px var(--primary-color-light);
}

.message-input:disabled {
  color: #999;
}

.send-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  min-width: 44px;
  min-height: 44px;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(240, 79, 61, 0.3);
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
}

.send-button svg {
  width: 22px;
  height: 22px;
}

@media (min-width: 768px) {
  .send-button {
    min-width: 46px;
    min-height: 46px;
    width: 46px;
    height: 46px;
    right: 0px;
  }

  .send-button svg {
    width: 24px;
    height: 24px;
  }
}

.send-button:hover {
  background-color: var(--primary-color-dark);
}

.send-button:disabled {
  background-color: #F8ADA5;
  cursor: not-allowed;
  box-shadow: none;
}

.send-status {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  z-index: 10;
  box-shadow: var(--shadow-sm);
  animation: fadeIn 0.3s ease-in-out;
}

@media (min-width: 768px) {
  .send-status {
    top: -40px;
    padding: 8px 16px;
    font-size: 14px;
  }
}

.send-status.success {
  background-color: #FEE8E6;
  color: var(--primary-color);
}

.send-status.error {
  background-color: #ffebee;
  color: #c62828;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

/* Attachment button styles */
.attachment-button {
  background-color: #f0f0f0;
  color: #666;
  border: none;
  min-width: 44px;
  min-height: 44px;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: background-color 0.2s;
  position: absolute;
  left: 2px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

@media (min-width: 768px) {
  .attachment-button {
    min-width: 46px;
    min-height: 46px;
    width: 46px;
    height: 46px;
    left: 2px;
  }
}

.attachment-button:hover {
  background-color: #e0e0e0;
}

.attachment-button:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

/* Attachment modal styles */
.attachment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.attachment-modal {
  background: white;
  border-radius: 12px;
  padding: 20px;
  min-width: 280px;
  max-width: 90vw;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.attachment-modal-header {
  text-align: center;
  margin-bottom: 20px;
}

.attachment-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.attachment-modal-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-option {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachment-option:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

.attachment-option.cancel {
  background: #fff;
  border-color: #dc3545;
  color: #dc3545;
  margin-top: 8px;
  justify-content: center;
}

.attachment-option.cancel:hover {
  background: #f8d7da;
}

/* Mobile-first styles are now the default */

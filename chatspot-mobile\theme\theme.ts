import { createTheme } from '@shopify/restyle';

// Define the color palette
const palette = {
  // Primary colors
  primary: '#F04F3D',
  primaryLight: '#F7A59F',
  primaryDark: '#C03A2B',

  // Neutrals
  white: '#FFFFFF',
  black: '#000000',
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',

  // Semantic colors
  success: '#10B981',
  error: '#EF4444',
  warning: '#F59E0B',
  info: '#3B82F6',
};

// Define spacing scale
const spacing = {
  none: 0,
  xs: 4,
  s: 8,
  m: 16,
  l: 24,
  xl: 32,
  xxl: 40,
  '2xl': 48,
  '3xl': 64,
};

// Define border radius scale
const borderRadii = {
  none: 0,
  xs: 2,
  s: 4,
  m: 8,
  l: 16,
  xl: 24,
  full: 9999,
};

// Define text variants
const textVariants = {
  defaults: {
    fontSize: 16,
    lineHeight: 24,
    color: 'text',
  },
  header: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 42,
    color: 'text',
  },
  subheader: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 36,
    color: 'text',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    lineHeight: 28,
    color: 'text',
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
    color: 'text',
  },
  bodyBold: {
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 24,
    color: 'text',
  },
  caption: {
    fontSize: 14,
    lineHeight: 20,
    color: 'textSecondary',
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 24,
    color: 'white',
  },
  link: {
    fontSize: 16,
    lineHeight: 24,
    color: 'primary',
    textDecorationLine: 'underline',
  },
};

// Define light theme
const lightTheme = createTheme({
  colors: {
    primary: palette.primary,
    primaryLight: palette.primaryLight,
    primaryDark: palette.primaryDark,
    background: palette.white,
    backgroundSecondary: palette.gray100,
    card: palette.white,
    text: palette.gray900,
    textSecondary: palette.gray600,
    border: palette.gray300,
    notification: palette.error,
    success: palette.success,
    error: palette.error,
    warning: palette.warning,
    info: palette.info,
    buttonPrimary: palette.primary,
    buttonSecondary: palette.gray200,
    buttonDisabled: palette.gray400,
    white: palette.white,
    black: palette.black,
  },
  spacing,
  borderRadii,
  textVariants,
  breakpoints: {
    phone: 0,
    tablet: 768,
  },
});

// Define dark theme
const darkTheme = {
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    primary: palette.primary,
    primaryLight: palette.primaryDark,
    primaryDark: palette.primaryLight,
    background: palette.gray900,
    backgroundSecondary: palette.gray800,
    card: palette.gray800,
    text: palette.gray50,
    textSecondary: palette.gray400,
    border: palette.gray700,
    buttonPrimary: palette.primary,
    buttonSecondary: palette.gray700,
    buttonDisabled: palette.gray600,
    white: palette.white,
    black: palette.black,
  },
};

export type Theme = typeof lightTheme;
export { lightTheme, darkTheme };

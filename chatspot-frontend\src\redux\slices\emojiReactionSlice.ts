import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

interface EmojiReaction {
  emoji: string;
  mood: string;
  timestamp: number;
}

interface EmojiReactionState {
  reactionUsers: Record<string, EmojiReaction>; // username -> reaction data
}

const initialState: EmojiReactionState = {
  reactionUsers: {}
};

const emojiReactionSlice = createSlice({
  name: 'emojiReaction',
  initialState,
  reducers: {
    setUserEmojiReaction: (state, action: PayloadAction<{
      userId: string,
      emoji: string | null,
      mood: string | null
    }>) => {
      const { userId, emoji, mood } = action.payload; // userId is actually username

      if (emoji && mood) {
        // Set the emoji reaction with timestamp
        state.reactionUsers[userId] = {
          emoji,
          mood,
          timestamp: Date.now()
        };
      } else {
        // Remove the user's emoji reaction
        delete state.reactionUsers[userId];
      }
    },

    clearEmojiReactions: (state) => {
      state.reactionUsers = {};
    }
  }
});

// Export actions
export const { setUserEmojiReaction, clearEmojiReactions } = emojiReactionSlice.actions;

// Export selectors
export const selectReactionUsers = (state: RootState) => state.emojiReaction.reactionUsers;

export const selectUserEmojiReaction = (state: RootState, username: string) => {
  const reaction = state.emojiReaction.reactionUsers[username];
  if (!reaction) return null;

  // Consider emoji reaction valid for 10 seconds (longer than typing)
  if ((Date.now() - reaction.timestamp) < 10000) {
    return reaction;
  }

  return null;
};

// Export reducer
export default emojiReactionSlice.reducer;

import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../auth/user.entity';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Ensure user is authenticated
    if (!user || !user.userId) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Check if user is an admin
    const dbUser = await this.userRepo.findOne({ where: { id: user.userId } });
    if (!dbUser || !dbUser.isAdmin) {
      throw new UnauthorizedException('User is not an admin');
    }

    return true;
  }
}

import React from 'react';
import { StyleSheet } from 'react-native';
import { withObservables } from '@nozbe/watermelondb/react';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import { of } from 'rxjs';
import Box from './Box';
import Text from './Text';

interface EnhancedMessageItemProps {
  message?: any; // This will be injected by withObservables
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

// The base component that receives the observed message
const MessageItemBase: React.FC<EnhancedMessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  const theme = useTheme<Theme>();

  if (!message) {
    return null;
  }

  // Format timestamp to show only time (12:34)
  const formattedTime = formatTime ? formatTime(message.timestamp) : new Date(message.timestamp).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });

  // Handle different message types
  if (message.type === 'clear_chat' || message.type === 'system') {
    return (
      <Box
        flexDirection="row"
        justifyContent="center"
        marginVertical="xs"
        width="100%"
      >
        <Box
          backgroundColor="backgroundSecondary"
          padding="s"
          borderRadius="m"
          style={styles.systemBubble}
        >
          <Text
            color="textSecondary"
            variant="caption"
            fontStyle="italic"
          >
            {message.message || `Chat cleared by ${message.is_mine ? 'you' : message.sender_username}`}
          </Text>
        </Box>
      </Box>
    );
  }

  if (message.type === 'typing') {
    return null;
  }

  const isMine = message.is_mine !== undefined ? message.is_mine :
                (message.isMine !== undefined ? message.isMine : false);
  const status = message.status || 'sent';

  // Regular message
  return (
    <Box
      flexDirection="row"
      justifyContent={isMine ? 'flex-end' : 'flex-start'}
      marginVertical="xs"
      width="100%"
    >
      <Box
        backgroundColor={isMine ? 'primary' : 'backgroundSecondary'}
        padding="m"
        borderRadius="m"
        maxWidth="80%"
        style={[
          styles.bubble,
          isMine ? styles.myBubble : styles.theirBubble,
        ]}
      >
        <Text
          color={isMine ? 'white' : 'text'}
          variant="body"
        >
          {message.message}
        </Text>

        <Box flexDirection="row" justifyContent="flex-end" marginTop="xs">
          <Text
            variant="caption"
            color={isMine ? 'white' : 'textSecondary'}
            opacity={0.7}
            fontSize={12}
          >
            {formattedTime}
          </Text>

          {isMine && status && (
            <Text
              variant="caption"
              color="white"
              opacity={0.7}
              marginLeft="xs"
              fontSize={12}
            >
              {status === 'sending' ? '' :
               status === 'sent' ? '✓' :
               status === 'delivered' ? '✓✓' :
               status === 'read' ? '✓✓' : ''}
            </Text>
          )}
        </Box>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  bubble: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  myBubble: {
    borderTopRightRadius: 2,
    borderBottomRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderTopLeftRadius: 16,
  },
  theirBubble: {
    borderTopLeftRadius: 2,
    borderBottomRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  systemBubble: {
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
  }
});

// Helper function to check if an object is a WatermelonDB model
const isWatermelonModel = (obj: any): boolean => {
  return obj && typeof obj.observe === 'function';
};

// Enhance the component with withObservables to make it reactive
const enhance = withObservables(['message'], ({ message }) => {
  // If message is a WatermelonDB model, return it directly
  if (isWatermelonModel(message)) {
    return { message };
  }

  // If message is a plain object, wrap it in an observable
  return {
    message: of(message)
  };
});

// Export the enhanced component
export default enhance(MessageItemBase);

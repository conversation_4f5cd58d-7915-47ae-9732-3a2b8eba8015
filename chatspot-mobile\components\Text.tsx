import React from 'react';
import { TextProps as RNTextProps } from 'react-native';
import { createText, TextProps as RestyleTextProps } from '@shopify/restyle';
import { Theme } from '../theme/theme';

const RestyleText = createText<Theme>();

type TextProps = RestyleTextProps<Theme> & RNTextProps;

export const Text = ({ color, ...rest }: TextProps) => {
  // Handle special case for 'white' color
  if (color === 'white') {
    return <RestyleText color="background" {...rest} />;
  }

  return <RestyleText color={color} {...rest} />;
};

export default Text;

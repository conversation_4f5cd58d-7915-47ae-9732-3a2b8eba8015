// src/components/ProfileWrapper.tsx
import { useParams } from 'react-router-dom';
import Profile from './Profile';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { logout } from '../redux/slices/authSlice';
import Settings from './Settings';
import './Chat.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleLeft } from '@fortawesome/free-solid-svg-icons';


const ProfileWrapper: React.FC = () => {
    const navigate = useNavigate();
  const dispatch = useDispatch();
  const { username } = useParams<{ username: string }>();

  if (!username) return <div>User not found</div>;


  const handleBackToChatRoom = () => {
    navigate(`/chat/${username}`);
  };

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  return (
    <div className="chat-container">
      <div className="chat-content">
        <div className="messages-section" style={{ flex: 1 }}>
          <div className="mobile-nav-controls active">
            {/*<button className="back-button" onClick={handleBackToChatRoom}>
              <span className="back-button-icon"><FontAwesomeIcon icon={faAngleLeft} size="xl"  /></span> Back
            </button>*/}
          </div>
          <div className="chat-window-container">
             <Profile
      username={username}
      onLogout={handleLogout}
      onClose={handleBackToChatRoom}
    />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileWrapper;
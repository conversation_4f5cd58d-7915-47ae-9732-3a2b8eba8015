import { useState } from 'react';
import './Settings.css';

interface ModalProps {
  type: 'password' | 'recall';
  onClose: () => void;
}

const SettingModal: React.FC<ModalProps> = ({ type, onClose }) => {
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setTimeout(() => {
      console.log(`Saved ${type}:`, value);
      setLoading(false);
      onClose();
    }, 800);
  };

  const renderContent = () => {
    if (type === 'password') {
      return (
        <div className="form-group">
          <label htmlFor="setting-password">New Password:</label>
          <input
            id="setting-password"
            type="password"
            placeholder="Enter new password"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            disabled={loading}
          />
        </div>
      );
    } else {
      return (
        <div className="form-group">
          <label htmlFor="setting-recall">Recall Limit (1–10):</label>
          <input
            id="setting-recall"
            type="number"
            min={1}
            max={10}
            value={value}
            onChange={(e) => setValue(e.target.value)}
            disabled={loading}
          />
        </div>
      );
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <p>{type === 'password' ? 'Change Password' : 'Set Recall Limit'}</p>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSave}>
          {renderContent()}

          <div className="modal-actions">
            <button type="button" className="cancel-button" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="start-chat-button" disabled={loading}>
              {loading ? 'Saving...' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SettingModal;

import React, { useState, useRef, useEffect } from 'react';
import { TextInput, StyleSheet, TouchableOpacity, Keyboard, Platform } from 'react-native';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import Box from './Box';
import { Ionicons } from '@expo/vector-icons';

interface MessageInputProps {
  onSend: (message: string) => void;
  onTyping: () => void;
  disabled?: boolean;
}

export const MessageInput = ({ onSend, onTyping, disabled = false }: MessageInputProps) => {
  const theme = useTheme<Theme>();
  const [message, setMessage] = useState('');
  const inputRef = useRef<TextInput>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle typing indicator
  const handleTextChange = (text: string) => {
    setMessage(text);
    
    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Trigger typing event
    onTyping();
    
    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      // Typing stopped
    }, 2000);
  };

  // Handle send message
  const handleSend = () => {
    if (message.trim() === '') return;
    
    onSend(message.trim());
    setMessage('');
    
    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    
    // Focus input after sending
    if (Platform.OS !== 'web') {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return (
    <Box
      flexDirection="row"
      alignItems="center"
      padding="m"
      backgroundColor="backgroundSecondary"
      borderTopWidth={1}
      borderTopColor="border"
    >
      <TextInput
        ref={inputRef}
        style={[
          styles.input,
          {
            color: theme.colors.text,
            backgroundColor: theme.colors.background,
            borderColor: theme.colors.border,
          },
        ]}
        value={message}
        onChangeText={handleTextChange}
        placeholder="Type a message..."
        placeholderTextColor={theme.colors.textSecondary}
        multiline
        maxLength={1000}
        editable={!disabled}
      />
      
      <TouchableOpacity
        style={[
          styles.sendButton,
          {
            backgroundColor: message.trim() === '' || disabled
              ? theme.colors.buttonDisabled
              : theme.colors.primary,
          },
        ]}
        onPress={handleSend}
        disabled={message.trim() === '' || disabled}
      >
        <Ionicons name="send" size={20} color="white" />
      </TouchableOpacity>
    </Box>
  );
};

const styles = StyleSheet.create({
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MessageInput;

import React, { ReactNode } from 'react';
import { ThemeProvider as RestyleThemeProvider } from '@shopify/restyle';
import { useColorScheme } from 'react-native';
import { lightTheme, darkTheme } from './theme';

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const colorScheme = useColorScheme();
  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;

  return <RestyleThemeProvider theme={theme}>{children}</RestyleThemeProvider>;
};

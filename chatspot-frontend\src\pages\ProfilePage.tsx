import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectUser, logout } from '../redux/slices/authSlice';
import Profile from '../components/Profile';
import '../components/Settings.css';

const ProfilePage: React.FC = () => {
  const { username } = useParams<{ username: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const currentUser = useSelector(selectUser);
  
  // Redirect if no username is provided
  useEffect(() => {
    if (!username) {
      navigate('/');
    }
  }, [username, navigate]);

  const handleClose = () => {
    // Go back to the previous page
    navigate(-1);
  };

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  return (
    <div className="mobile-page">
      <Profile 
        username={username || ''} 
        onClose={handleClose}
        onLogout={handleLogout}
      />
    </div>
  );
};

export default ProfilePage;

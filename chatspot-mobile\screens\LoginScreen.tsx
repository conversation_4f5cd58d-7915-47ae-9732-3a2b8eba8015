
import React, { useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { loginRequest, selectAuthLoading, selectAuthError, clearError, selectIsAuthenticated, selectAuthToken } from '../redux/slices/authSlice';
import { connectRequest } from '../redux/slices/socketSlice';
import Box from '../components/Box';
import Text from '../components/Text';
import Input from '../components/Input';
import Button from '../components/Button';

export const LoginScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const isLoading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authToken = useSelector(selectAuthToken);

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [usernameError, setUsernameError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // Clear any auth errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Connect socket when authenticated
  useEffect(() => {
    if (isAuthenticated && authToken) {
      console.log('User authenticated, connecting to socket with token:', authToken?.substring(0, 10) + '...');
      dispatch(connectRequest({ authToken }));
    }
  }, [isAuthenticated, authToken, dispatch]);

  const validateForm = () => {
    let isValid = true;

    if (!username.trim()) {
      setUsernameError('Username is required');
      isValid = false;
    } else {
      setUsernameError('');
    }

    if (!password) {
      setPasswordError('Password is required');
      isValid = false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      isValid = false;
    } else {
      setPasswordError('');
    }

    return isValid;
  };

  const handleLogin = () => {
    if (validateForm()) {
      dispatch(loginRequest({ username, password }));
    }
  };

  const navigateToRegister = () => {
    navigation.navigate('Register' as never);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <Box padding="xl" width="100%" maxWidth={400} alignSelf="center">
          <Text variant="header" textAlign="center" marginBottom="xl">
            ChatSpot Messenger
          </Text>

          <Text variant="title" marginBottom="l">
            Login
          </Text>

          {error && (
            <Box
              backgroundColor="error"
              padding="m"
              borderRadius="m"
              marginBottom="m"
            >
              <Text color="white">{error}</Text>
            </Box>
          )}

          <Input
            label="Username"
            value={username}
            onChangeText={setUsername}
            placeholder="Enter your username"
            autoCapitalize="none"
            error={usernameError}
          />

          <Input
            label="Password"
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            secureTextEntry
            error={passwordError}
          />

          <Button
            label="Login"
            onPress={handleLogin}
            loading={isLoading}
            fullWidth
            variant="primary"
          />

          <Box flexDirection="row" justifyContent="center" marginTop="l">
            <Text variant="body">Don't have an account? </Text>
            <TouchableOpacity onPress={navigateToRegister}>
              <Text variant="link">Register</Text>
            </TouchableOpacity>
          </Box>
        </Box>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
});

export default LoginScreen;


import React, { useEffect, useRef, useState } from 'react';
import { FlatList, StyleSheet, ActivityIndicator, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation, useRoute } from '@react-navigation/native';
import { selectUser } from '../redux/slices/authSlice';
import { sendMessageRequest, selectConnected } from '../redux/slices/socketSlice';
import { setCurrentReceiver } from '../redux/slices/chatDBSlice';
import { setIsTyping, selectTypingUsers } from '../redux/slices/typingSlice';
import { chatDBService } from '../database/service';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import Box from '../components/Box';
import Text from '../components/Text';
import ChatHeader from '../components/ChatHeader';
import MessageInput from '../components/MessageInput';
import MessageBubble from '../components/MessageBubble';
import EnhancedMessageItem from '../components/EnhancedMessageItem';
import { Subscription } from 'rxjs';

export const ChatScreen = () => {
  // Hooks
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const theme = useTheme<Theme>();

  // Redux selectors
  const currentUser = useSelector(selectUser);
  const isConnected = useSelector(selectConnected);
  const typingUsers = useSelector(selectTypingUsers);

  // State
  const [messages, setMessages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isChatCleared, setIsChatCleared] = useState(false);

  // Refs
  const flatListRef = useRef<FlatList>(null);
  const messageSubscription = useRef<Subscription | null>(null);

  // Get route params
  const params = route.params as { receiverUsername: string; username?: string };
  const receiverUsername = params.receiverUsername;
  const username = params.username || receiverUsername; // Use provided username or fallback to receiverUsername

  useEffect(() => {
    console.log('ChatScreen mounted with currentUser:', currentUser, 'receiverUsername:', receiverUsername);

    // Set current receiver in Redux
    if (receiverUsername) {
      dispatch(setCurrentReceiver(receiverUsername));
    }

    // Mark messages as read
    if (currentUser && receiverUsername) {
      chatDBService.markMessagesAsRead(currentUser, receiverUsername)
        .catch(error => console.error('Error marking messages as read:', error));
    }

    // Load messages
    loadMessages();

    // Cleanup subscription on unmount
    return () => {
      if (messageSubscription.current) {
        messageSubscription.current.unsubscribe();
      }
    };
  }, [currentUser, receiverUsername]);

  const loadMessages = () => {
    if (!currentUser || !receiverUsername) return;

    setIsLoading(true);

    // Clean up existing subscription
    if (messageSubscription.current) {
      messageSubscription.current.unsubscribe();
      messageSubscription.current = null;
    }

    console.log(`Setting up message subscription for ${currentUser} and ${receiverUsername}`);
    // Use observeMessageRecords to get raw records for individual reactivity
    const observable = chatDBService.observeMessageRecords(currentUser, receiverUsername);
    messageSubscription.current = observable.subscribe(newMessages => {
      console.log(`Received ${Array.isArray(newMessages) ? newMessages.length : 0} messages from observable`);

      if (Array.isArray(newMessages) && newMessages.length > 0) {
        console.log('Message sample:', JSON.stringify(newMessages[0]));
      }

      if (Array.isArray(newMessages)) {
        setMessages(newMessages);
        setIsLoading(false);
      } else {
        console.error('Received non-array messages:', newMessages);
        setIsLoading(false);
      }
    });
  };

  const handleSendMessage = (messageText: string) => {
    if (!currentUser || !receiverUsername || !isConnected) return;

    dispatch(sendMessageRequest({
      receiverUsername: receiverUsername,
      messageText,
      messageType: 'text'
    }));
  };

  const handleTyping = () => {
    if (!currentUser || !receiverUsername || !isConnected) return;

    dispatch(setIsTyping(true));
    dispatch(sendMessageRequest({
      receiverUsername: receiverUsername,
      messageText: '',
      messageType: 'typing'
    }));
  };

  const handleClearChat = () => {
    if (!currentUser || !receiverUsername || !isConnected) return;

    Alert.alert(
      'Clear Chat',
      'Are you sure you want to clear all messages? This cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              await chatDBService.clearRoom(currentUser, receiverUsername);
              console.log('Chat cleared successfully');
              setIsChatCleared(true);

              dispatch(sendMessageRequest({
                receiverUsername: receiverUsername,
                messageText: 'Chat cleared',
                messageType: 'clear_chat'
              }));

              setMessages([]);
              setIsLoading(false);
            } catch (error) {
              console.error('Failed to clear chat:', error);
              setIsLoading(false);
              Alert.alert('Error', 'Failed to clear chat. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleDeleteUser = () => {
    if (!currentUser || !receiverUsername || !isConnected) return;

    Alert.alert(
      'Delete User',
      'Are you sure you want to delete this user and all messages? This cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              await chatDBService.deleteUserRoom(currentUser, receiverUsername);
              console.log('User deleted successfully');

              dispatch(sendMessageRequest({
                receiverUsername: receiverUsername,
                messageText: 'User deleted',
                messageType: 'delete_user'
              }));

              navigation.goBack();
            } catch (error) {
              console.error('Failed to delete user:', error);
              setIsLoading(false);
              Alert.alert('Error', 'Failed to delete user. Please try again.');
            }
          }
        }
      ]
    );
  };

  const renderMessageItem = ({ item, index }: { item: any; index: number }) => {
    const itemId = item?.id || `msg_${index}`;
    console.log(`Rendering message ${index} with ID: ${itemId}`);

    try {
      // Format time function for the message
      const formatTime = (timestamp: number) => {
        return new Date(timestamp).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        });
      };

      // Check if this message is the last in a group
      const isLastInGroup = (index: number) => {
        if (index === messages.length - 1) return true;

        const nextMessage = messages[index + 1];
        if (!nextMessage) return true;

        const currentIsMine = item?.is_mine !== undefined ? item.is_mine :
                            (item?.isMine !== undefined ? item.isMine : false);
        const nextIsMine = nextMessage?.is_mine !== undefined ? nextMessage.is_mine :
                          (nextMessage?.isMine !== undefined ? nextMessage.isMine : false);

        return currentIsMine !== nextIsMine;
      };

      // Use the EnhancedMessageItem component for reactive updates
      return (
        <EnhancedMessageItem
          key={itemId}
          message={item}
          formatTime={formatTime}
          isLastInGroup={isLastInGroup(index)}
        />
      );
    } catch (error) {
      console.error('Error rendering message:', error, item);
      return null;
    }
  };

  const renderTypingIndicator = () => {
    const isOtherUserTyping = typingUsers[receiverUsername] || false;

    if (!isOtherUserTyping) return null;

    return (
      <Box flexDirection="row" alignItems="center" padding="m" marginBottom="s">
        <Text variant="caption" color="textSecondary">
          {username} is typing...
        </Text>
      </Box>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <ChatHeader
        title={username}
        showBackButton={true}
        onClearChat={handleClearChat}
        onDeleteUser={handleDeleteUser}
      />

      <Box flex={1} backgroundColor="background">
        {isLoading ? (
          <Box flex={1} justifyContent="center" alignItems="center">
            <ActivityIndicator size="large" color={theme.colors.primary} />
          </Box>
        ) : (
          <>
            <FlatList
              ref={flatListRef}
              data={messages}
              renderItem={renderMessageItem}
              keyExtractor={(item, index) => {
                if (!item) return `empty_${index}`;
                const id = item.id || item._id || item._raw?.id;
                return id ? String(id) : `msg_${index}`;
              }}
              contentContainerStyle={styles.messageList}
              inverted={false}
              ListEmptyComponent={
                <Box flex={1} justifyContent="center" alignItems="center" padding="xl">
                  <Text variant="body" color="textSecondary" textAlign="center">
                    {isChatCleared
                      ? 'Chat has been cleared'
                      : 'No messages yet. Start the conversation!'}
                  </Text>
                </Box>
              }
              ListFooterComponent={renderTypingIndicator}
            />

            <MessageInput
              onSend={handleSendMessage}
              onTyping={handleTyping}
              disabled={!isConnected}
            />
          </>
        )}
      </Box>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  messageList: {
    padding: 16,
    flexGrow: 1,
  },
});

export default ChatScreen;


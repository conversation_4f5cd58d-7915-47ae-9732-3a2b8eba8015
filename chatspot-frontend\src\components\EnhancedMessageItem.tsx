import React from 'react';
import { withObservables } from '@nozbe/watermelondb/react';
import { of } from 'rxjs';

interface MessageItemProps {
  message?: any; // This will be injected by withObservables
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

// The base component that receives the observed message
const MessageItemBase: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  if (!message) {
    return null;
  }

  // Handle different message types
  if (message.type === 'clear_chat') {
    return (
      <div className="message-system">
        <div className="message-content system">
          <p>Chat cleared by {message.is_mine ? 'you' : message.sender_username}</p>
          <span className="message-time">{formatTime(message.timestamp)}</span>
        </div>
      </div>
    );
  }

  if (message.type === 'typing') {
    return null;
  }
  if(isLastInGroup) {
    console.log('last in group==========>', message.status)
  }

  // Handle media messages
  const renderMessageContent = () => {
    switch (message.type) {
      case 'image':
        return (
          <div className="media-message">
            <img
              src={message.fileUrl || message.file_url}
              alt={message.fileName || message.file_name || 'Image'}
              className="message-image"
              loading="lazy"
            />
            {message.message && message.message !== (message.fileName || message.file_name) && (
              <p className="media-caption">{message.message}</p>
            )}
          </div>
        );

      case 'video':
        return (
          <div className="media-message">
            <video
              src={message.fileUrl || message.file_url}
              controls
              className="message-video"
              preload="metadata"
            >
              Your browser does not support the video tag.
            </video>
            {message.message && message.message !== (message.fileName || message.file_name) && (
              <p className="media-caption">{message.message}</p>
            )}
          </div>
        );

      case 'file':
        return (
          <div className="file-message">
            <div className="file-info">
              <div className="file-icon">📄</div>
              <div className="file-details">
                <span className="file-name">{message.fileName || message.file_name}</span>
                <span className="file-size">
                  {message.fileSize || message.file_size ?
                    formatFileSize(message.fileSize || message.file_size) :
                    'Unknown size'
                  }
                </span>
              </div>
              <a
                href={message.fileUrl || message.file_url}
                download={message.fileName || message.file_name}
                className="file-download"
              >
                ⬇️
              </a>
            </div>
            {message.message && message.message !== (message.fileName || message.file_name) && (
              <p className="media-caption">{message.message}</p>
            )}
          </div>
        );

      default:
        return <p>{message.message}</p>;
    }
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Regular message
  return (
    <div
      className={`message ${message.isMine ? 'sent' : 'received'} ${isLastInGroup ? 'last-in-group' : ''
        }`}
    >
      <div className="message-content">
        {renderMessageContent()}
        <div className="message-info">
          <span className="message-time">{formatTime(message.timestamp)}</span>
          {message.isMine && (
            <span className="message-status">
              {message.status === 'sending' ? 'sending...' :
                message.status === 'sent' ? '✓' :
                  message.status === 'delivered' ? '✓✓' :
                    message.status === 'read' ? '✓✓' : ''}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

// Enhance the component with withObservables to make it reactive
const enhance = withObservables(['message'], ({ message }) => ({ message }));

// Export the enhanced component
export default enhance(MessageItemBase);

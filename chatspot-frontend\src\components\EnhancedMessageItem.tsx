import React from 'react';
import { withObservables } from '@nozbe/watermelondb/react';
import { of } from 'rxjs';

interface MessageItemProps {
  message?: any; // This will be injected by withObservables
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

// The base component that receives the observed message
const MessageItemBase: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  if (!message) {
    return null;
  }

  // Handle different message types
  if (message.type === 'clear_chat') {
    return (
      <div className="message-system">
        <div className="message-content system">
          <p>Chat cleared by {message.is_mine ? 'you' : message.sender_username}</p>
          <span className="message-time">{formatTime(message.timestamp)}</span>
        </div>
      </div>
    );
  }

  if (message.type === 'typing') {
    return null;
  }
  if(isLastInGroup) {
    console.log('last in group==========>', message.status)
  }

  // Regular message
  return (
    <div
      className={`message ${message.isMine ? 'sent' : 'received'} ${isLastInGroup ? 'last-in-group' : ''
        }`}
    >
      <div className="message-content">
        <p>{message.message}</p>
        <div className="message-info">
          <span className="message-time">{formatTime(message.timestamp)}</span>
          {message.isMine && (
            <span className="message-status">
              {message.status === 'sending' ? 'sending...' :
                message.status === 'sent' ? '✓' :
                  message.status === 'delivered' ? '✓✓' :
                    message.status === 'read' ? '✓✓' : ''}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

// Enhance the component with withObservables to make it reactive
const enhance = withObservables(['message'], ({ message }) => ({ message }));

// Export the enhanced component
export default enhance(MessageItemBase);

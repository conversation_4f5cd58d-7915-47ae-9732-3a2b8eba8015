import { useNavigate, useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectConnected } from '../redux/slices/socketSlice';
import { selectIsUserTyping } from '../redux/slices/typingSlice';
import { RootState } from '../redux/store';
import './Chat.css';
import './UserInfo.css';

const UserProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const { username } = useParams<{ username: string }>();
  const connected = useSelector(selectConnected);
  
  // Get typing status from Redux
  const isTyping = username ? useSelector((state: RootState) =>
    selectIsUserTyping(state, username)
  ) : false;

  const chatStatus = connected ? (isTyping ? 'Typing' : 'Online') : 'Offline';

  const handleClose = () => {
    navigate('/chat');
  };

  // Display first letter of username as avatar
  const getAvatarText = () => {
    return username ? username.charAt(0).toUpperCase() : '';
  };

  return (
    <div className="chat-container">
      <div className="chat-content">
        <div className="messages-section" style={{ flex: 1 }}>
          <div className="mobile-nav-controls active">
            <button className="back-button" onClick={handleClose}>
              <span className="back-button-icon">←</span> Back to Chat
            </button>
          </div>
          <div className="user-profile-container">
            <div className="user-profile-header">
              <div>USER PROFILE</div>
            </div>
            
            <div className="user-profile-info">
              <div className="user-avatar-large">{getAvatarText()}</div>
              <div className="username-display">{username}</div>
              <div className="user-status">{chatStatus}</div>
            </div>
            
            <div className="user-profile-actions">
              <button className="profile-action-button" onClick={handleClose}>
                Return to Chat
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;

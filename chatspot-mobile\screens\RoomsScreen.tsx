import React, { useState, useEffect, useMemo } from 'react';
import { FlatList, StyleSheet, TouchableOpacity, Alert, TextInput } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { logout, selectUser, selectAuthToken } from '../redux/slices/authSlice';
import { selectConnected, connectRequest } from '../redux/slices/socketSlice';
import { useWatermelonObservable } from '../hooks/useWatermelonObservable';
import { chatDBService } from '../database/service';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import Box from '../components/Box';
import Text from '../components/Text';
import Button from '../components/Button';
import EnhancedRoomListItem from '../components/EnhancedRoomListItem';

export const RoomsScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const theme = useTheme<Theme>();

  // Redux state
  const currentUser = useSelector(selectUser);
  const isConnected = useSelector(selectConnected);
  const authToken = useSelector(selectAuthToken);

  // Local state
  const [showNewChatModal, setShowNewChatModal] = useState(false);
  const [newChatUsername, setNewChatUsername] = useState('');

  // We'll use the roomsData from useWatermelonObservable directly

  // Initialize database
  useEffect(() => {
    if (!currentUser) return;

    // Initialize the database
    chatDBService.initialize()
      .then(initialized => {
        if (initialized) {
          console.log('Database initialized successfully');
        } else {
          console.error('Failed to initialize database');
        }
      })
      .catch(error => {
        console.error('Error initializing database:', error);
      });
  }, [currentUser]);

  // Memoize the observable to prevent recreating it on every render
  const roomsObservable = useMemo(() => {
    return currentUser ? chatDBService.observeRooms(currentUser) : null;
  }, [currentUser]);

  // Observe rooms reactively
  const roomsData = useWatermelonObservable(roomsObservable, []);
console.log('roomsData', roomsData)
  // We'll use roomsData directly from the hook in the FlatList

  // Connect to socket when component mounts or when auth token changes
  useEffect(() => {
    if (authToken) {
      console.log('Connecting to socket with token...');
      dispatch(connectRequest({ authToken }));
    }
  }, [authToken, dispatch]);

  // Auto-reconnect socket when disconnected
  useEffect(() => {
    // If we have auth token but not connected, try to reconnect
    if (authToken && !isConnected) {
      const reconnectTimer = setTimeout(() => {
        console.log('Auto-reconnecting to socket...');
        dispatch(connectRequest({ authToken }));
      }, 5000); // Try to reconnect every 5 seconds

      return () => clearTimeout(reconnectTimer);
    }
  }, [isConnected, authToken, dispatch]);

  // Handle logout
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => dispatch(logout())
        }
      ]
    );
  };

  // Handle starting a new chat
  const handleStartChat = () => {
    if (!newChatUsername.trim()) {
      Alert.alert('Error', 'Please enter a username');
      return;
    }

    // Prevent chatting with yourself
    if (newChatUsername === currentUser) {
      Alert.alert('Error', 'You cannot chat with yourself');
      return;
    }

    // In a real app, you would validate the username exists
    // For now, we'll just create a chat with the entered username
    // @ts-ignore - Navigation typing issue
    navigation.navigate('Chat', {
      receiverUsername: newChatUsername,
      username: newChatUsername
    });

    setShowNewChatModal(false);
    setNewChatUsername('');
  };

  // Render a room item
  const renderRoomItem = ({ item }: { item: any }) => {
    // Get the other username from the room item
    console.log('Room item:', JSON.stringify(item));

    // Get the other username, with fallbacks
    const otherUsername = item.otherUsername || item.other_username || '';

    return (
      <EnhancedRoomListItem
        roomId={item.id}
        onPress={() => {
          // @ts-ignore - Navigation typing issue
          navigation.navigate('Chat', {
            receiverUsername: otherUsername,
            username: otherUsername
          });
        }}
      />
    );
  };

  // Render the new chat modal
  const renderNewChatModal = () => {
    if (!showNewChatModal) return null;

    return (
      <Box
        position="absolute"
        bottom={0}
        left={0}
        right={0}
        backgroundColor="card"
        padding="l"
        borderTopLeftRadius="l"
        borderTopRightRadius="l"
        shadowColor="black"
        shadowOpacity={0.2}
        shadowRadius={10}
        elevation={5}
      >
        <Box flexDirection="row" justifyContent="space-between" alignItems="center" marginBottom="m">
          <Text variant="title">New Chat</Text>
          <TouchableOpacity onPress={() => setShowNewChatModal(false)}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </Box>

        <TextInput
          style={[
            styles.input,
            {
              color: theme.colors.text,
              backgroundColor: theme.colors.backgroundSecondary,
              borderColor: theme.colors.border,
            },
          ]}
          value={newChatUsername}
          onChangeText={setNewChatUsername}
          placeholder="Enter username"
          placeholderTextColor={theme.colors.textSecondary}
          autoCapitalize="none"
        />

        <Button
          label="Start Chat"
          onPress={handleStartChat}
          variant="primary"
          fullWidth
        />
      </Box>
    );
  };

  return (
    <Box flex={1} backgroundColor="background">
      <Box
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        backgroundColor="primary"
        paddingHorizontal="m"
        paddingVertical="m"
        height={56}
      >
        <Text variant="title" color="white">
          {currentUser || 'ChatSpot'}
        </Text>

        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <Ionicons name="log-out-outline" size={24} color="white" />
        </TouchableOpacity>
      </Box>

      <Box flex={1}>
        {!isConnected && (
          <Box
            backgroundColor="error"
            padding="s"
            alignItems="center"
          >
            <Text color="white" marginBottom="xs">
              Disconnected from chat server
            </Text>
            <Button
              label="Reconnect"
              onPress={() => {
                if (authToken) {
                  console.log('Manually reconnecting to socket...');
                  dispatch(connectRequest({ authToken }));
                }
              }}
              variant="secondary"
            />
          </Box>
        )}

        <FlatList
          data={roomsData}
          renderItem={renderRoomItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.roomList}
          ListEmptyComponent={
            <Box flex={1} justifyContent="center" alignItems="center" padding="xl">
              <Text variant="body" color="textSecondary" textAlign="center">
                No conversations yet. Start a new chat!
              </Text>
            </Box>
          }
        />

        <TouchableOpacity
          style={[
            styles.newChatButton,
            { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setShowNewChatModal(true)}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </Box>

      {renderNewChatModal()}
    </Box>
  );
};

const styles = StyleSheet.create({
  roomList: {
    flexGrow: 1,
  },
  logoutButton: {
    padding: 8,
  },
  newChatButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 16,
  },
});

export default RoomsScreen;

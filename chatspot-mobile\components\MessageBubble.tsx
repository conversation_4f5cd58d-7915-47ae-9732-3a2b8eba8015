import React from 'react';
import { StyleSheet } from 'react-native';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';
import Box from './Box';
import Text from './Text';

interface MessageBubbleProps {
  message: string;
  timestamp: string;
  isMine: boolean;
  status?: string;
}

export const MessageBubble = ({ message, timestamp, isMine, status }: MessageBubbleProps) => {
  const theme = useTheme<Theme>();

  // Format timestamp to show only time (12:34)
  const formattedTime = new Date(timestamp).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });

  return (
    <Box
      flexDirection="row"
      justifyContent={isMine ? 'flex-end' : 'flex-start'}
      marginVertical="xs"
      width="100%"
    >
      <Box
        backgroundColor={isMine ? 'primary' : 'backgroundSecondary'}
        padding="m"
        borderRadius="m"
        maxWidth="80%"
        style={[
          styles.bubble,
          isMine ? styles.myBubble : styles.theirBubble,
        ]}
      >
        <Text
          color={isMine ? 'white' : 'text'}
          variant="body"
        >
          {message}
        </Text>

        <Box flexDirection="row" justifyContent="flex-end" marginTop="xs">
          <Text
            variant="caption"
            color={isMine ? 'white' : 'textSecondary'}
            opacity={0.7}
            fontSize={12}
          >
            {formattedTime}
          </Text>

          {isMine && status && (
            <Text
              variant="caption"
              color="white"
              opacity={0.7}
              marginLeft="xs"
              fontSize={12}
            >
              {status === 'sent' ? '✓' : status === 'delivered' ? '✓✓' : status === 'read' ? '✓✓' : ''}
            </Text>
          )}
        </Box>
      </Box>
    </Box>
  );
};

const styles = StyleSheet.create({
  bubble: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  myBubble: {
    borderTopRightRadius: 2,
    borderBottomRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderTopLeftRadius: 16,
  },
  theirBubble: {
    borderTopLeftRadius: 2,
    borderBottomRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderTopRightRadius: 16,
  },
});

export default MessageBubble;

.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out;
}

.splash-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo-container {
  margin-bottom: 20px;
  position: relative;
}

.chat-icon {
  width: 80px;
  height: 80px;
  background-color: var(--primary-color);
  border-radius: 20px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: rotate(-5deg);
  animation: pulse 2s infinite;
  box-shadow: 0 6px 20px rgba(240, 80, 62, 0.3);
}

.chat-bubble {
  position: absolute;
  background-color: white;
  border-radius: 50%;
}

.chat-bubble:nth-child(1) {
  width: 20px;
  height: 20px;
  top: 20px;
  left: 20px;
  animation: float 3s infinite;
}

.chat-bubble:nth-child(2) {
  width: 15px;
  height: 15px;
  bottom: 20px;
  right: 20px;
  animation: float 3s infinite 0.5s;
}

.app-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 10px 0;
  letter-spacing: 1px;
  animation: fadeIn 1s ease-in;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.loading-dot {
  width: 10px;
  height: 10px;
  background-color: var(--primary-color);
  border-radius: 50%;
  margin: 0 5px;
  animation: loadingDots 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0% {
    transform: rotate(-5deg) scale(1);
  }
  50% {
    transform: rotate(-5deg) scale(1.05);
  }
  100% {
    transform: rotate(-5deg) scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loadingDots {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

/* Fade out animation */
.splash-screen.fade-out {
  opacity: 0;
  pointer-events: none;
}

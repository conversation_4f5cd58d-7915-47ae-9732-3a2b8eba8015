.chat-window {
  display: flex;
  flex-direction: column;
  position: relative;
  height: calc(var(--vh, 1vh) * 100);
}



@media (min-width: 768px) {
  .chat-window {
    border-radius: 0 0 var(--radius-md) 0; /* Restore border radius on desktop */
  }
}

.chat-window-header {
  display: flex;
  top: 0;
  z-index: 10;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 10px 16px;
}

@media (max-width: 767px) {
  .chat-window-header {
    width: 100%;
  }

}






.back-button {
  background-color: transparent;
  border: none;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-right: 8px;
}

.back-button:hover {
  background-color: rgba(240, 79, 61, 0.1);
  border-radius: 4px;
}

.back-button-icon {
  font-size: 18px;
}

@media (min-width: 768px) {
  .chat-window-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: rgba(255, 255, 255, 0.1); /* Glassy transparent white */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

}

/* Chat contact info styles */
.chat-contact-info {
  flex: 1;
}

.chat-contact-info .user-avatar {
  width: 38px;
  height: 38px;
  font-size: 16px;
  margin-right: 8px;
  background-color: var(--tint-color-light);
}

@media (min-width: 768px) {
  .chat-contact-info .user-avatar {
    width: 50px;
    height: 50px;
    font-size: 18px;
    margin-right: 15px;
  }
}

.chat-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (min-width: 768px) {
  .chat-header-actions {
    gap: 15px;
  }
}

.chat-header-buttons {
  display: flex;
  gap: 8px;
}

@media (min-width: 768px) {
  .chat-header-buttons {
    gap: 10px;
  }
}

.chat-contact-status {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

@media (min-width: 768px) {
  .chat-contact-status {
    font-size: 13px;
    gap: 5px;
  }
}

.typing-text {
  margin-right: 2px;
}

.header-typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 15px;
}

.header-typing-indicator span {
  height: 3px;
  width: 3px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: inline-block;
  margin: 0 1px;
  opacity: 0.8;
  animation: typing 1.4s infinite both;
}

@media (min-width: 768px) {
  .header-typing-indicator span {
    height: 4px;
    width: 4px;
  }
}

.header-typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.header-typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

.clear-chat-btn, .delete-user-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  padding: 5px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 36px;
  min-width: 36px; /* Square buttons on mobile */
}

.clear-chat-btn {
  color: #000;
}

.delete-user-btn {
  color: #000;
}

@media (min-width: 768px) {
  .clear-chat-btn, .delete-user-btn {
    padding: 5px 10px;
    font-size: 13px;
    min-height: 36px;
    min-width: auto; /* Reset min-width on desktop */
  }
}

.clear-chat-btn:hover {
  background-color: orange;
  box-shadow: none;
  border-radius: 45px;
}

.delete-user-btn:hover {
  background-color: red;
    box-shadow: none;
    border-radius: 45px;

}

.clear-icon, .delete-icon {
  margin-right: 0;
  font-size: 14px;
  width: 20px;
}



.clear-text, .delete-text {
  display: none;
}

@media (min-width: 768px) {
  .clear-icon, .delete-icon {
  }

  .clear-text, .delete-text {
    display: inline;
  }
}


.messages-container {
  flex: 1;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column-reverse; /* Optional: bottom-up messages */
  background: linear-gradient(to top, var(--secondary-color-light), var(--tint-color-light));
   background-image: 
    repeating-linear-gradient(
      45deg,         /* Angle of lines */
      #d7d4d4,          /* Line color */
      #d7d4d4 1px,      /* Line thickness */
      transparent 2px,
      transparent 5px
    );
}



@media (min-width: 768px) {
  .messages-container {
     background-color: transparent;
      margin-bottom: 0px;

  }
}


.messages-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0px 16px;
}

.message {
  max-width: 90%; /* Even wider for mobile */
  margin: 2px 0;
}

@media (min-width: 768px) {
  .message {
    max-width: 75%;
  }
}

.message.sent {
  align-self: flex-end;
}

.message.received {
  align-self: flex-start;
}

.message-date-separator {
  display: flex;
  justify-content: center;
  margin: 12px 0;
  position: relative;
}

.message-date-separator span {
  background-color: var(--tint-color);
  color: var(--tint-color-light);
  font-size: 11px;
  padding: 4px 10px;
  border-radius: 7px;
  z-index: 1;
  font-weight: 500;
}

@media (min-width: 768px) {
  .message-date-separator span {
    font-size: 12.5px;
    padding: 5px 12px;
  }
}

.message-content {
  padding: 8px 18px;
  border-radius: 10px;
  position: relative;
}

@media (min-width: 768px) {
  .message-content {
  padding: 8px 18px;
  min-width: 65px;
  }
}

.sent .message-content {
  background-color:var(--primary-color-tint) ; /* Light red for sent messages */
  border-bottom-right-radius: 0;
  box-shadow: 0 0 3px var(--primary-color-light);
}

.received .message-content {
  background-color: var(--tint-color-light);
  border-bottom-left-radius: 0;
  box-shadow: 0 0 3px var(--shade-color-light);
}

.message-content p {
  word-wrap: break-word;
  font-size: 17px;
  line-height: 20px;
}

.received .message-content p {
  color: var(--shade-color-two);
  text-align: left;

}

.sent .message-content p {
  color: var(--primary-color);
  text-align: right;
}

.sent .message-content {
 text-align: right;
}

.received .message-content {
  text-align: left;
}

@media (min-width: 768px) {
  .message-content p {
    font-size: 15px;
    line-height: 20px;
  }
}

.message-info {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
}

.message-time {
  font-size: 0.7em;
  color: var(--text-secondary);
}

.message-status {
  font-size: 0.8em;
  color: var(--primary-color);
}

.sent .message-status {
  color: var(--primary-color);
}

@media (min-width: 768px) {
  .message-info {
    gap: 5px;
    margin-top: 3px;
  }

  .message-time {
    font-size: 0.7em;
  }

  .message-status {
    font-size: 0.8em;
  }
}

/* System messages (like clear chat) */
.message-system {
  align-self: center;
  max-width: 85%;
  margin: 8px 0;
}

.message-content.system {
  background-color: #f0f0f0;
  color: #666;
  text-align: center;
  border: none;
  border-radius: 15px;
  padding: 5px 12px;
}

.message-content.system p {
  font-style: italic;
  font-size: 13px;
  color: var(--shade-color);
}

/* Typing indicator */
.message-typing {
  align-self: flex-start;
  margin: 2px 0;
}

.typing-indicator {
  display: flex;
  align-items: center;
  background-color: #f1f1f1;
  border-radius: 15px;
  padding: 8px 12px;
  width: 50px;
  justify-content: center;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: #999;
  border-radius: 50%;
  display: inline-block;
  margin: 0 2px;
  opacity: 0.6;
  animation: typing 1.4s infinite both;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

.no-messages {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--shade-color-two);
  text-align: center;
  border-radius: var(--radius-md);
  font-size: 17px;
  background-color: #ffffff88;
}

@media (min-width: 768px) {
  .no-messages {
    padding: 20px;
    margin: 20px;
    font-size: 16px;
  }
}

/* Connection status banner */
.connection-status-banner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 10px;
  background-color: var(--shade-color);
  border-bottom: 1px solid #ffcdd2;
  color: #fff;
  font-size: 14px;
  flex-wrap: wrap;
  gap: 10px;
}

.connection-status-message {
  text-align: center;
}

.reconnect-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
  padding: 0px 5px;
}

.reconnect-button:hover {
  background-color: #b71c1c;
}

@media (min-width: 768px) {
  .connection-status-banner {
    padding: 10px 16px;
    font-size: 15px;
  }

  .reconnect-button {
    padding: 6px 12px;
    font-size: 14px;
  }
}


/* Mobile-first styles are now the default */

.chat-contact-info .user-name {
  font-size: 17px;
}

@media (min-width: 768px) {
  .chat-contact-info .user-name {
    font-size: 16px;
  }
}

/* Emoji reaction message */
.emoji-reaction-message {
  margin-bottom: 8px;
  border-radius: 10px;
}

.emoji-reaction-emoji {
  font-size: 40px;
  margin-right: 8px;
}

.emoji-reaction-mood {
  font-size: 14px;
  color: inherit;
  font-weight: 500;
  text-transform: capitalize;
}

/* Animation for emoji reactions */
@keyframes pulseEmoji {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Mobile-specific styles for emoji reaction */
@media (max-width: 767px) {
  .emoji-reaction-emoji {
    font-size: 40px;
    animation: pulseEmoji 1.5s infinite;
  }

  .emoji-reaction-mood {
    font-size: 13px;
  }
}

.emoji-message-content {
  padding: 8px 18px;
  border-radius: 10px;
  position: relative;
  background-color: var(--shade-color-two);
}

.sent .emoji-message-content {
  border-bottom-right-radius: 0;
}

.received .emoji-message-content {
  border-bottom-left-radius: 0;
}



.message.sent .message-content,
.message.received .message-content {
  border-radius: 10px;
}

.message.sent.last-in-group .message-content {
  border-bottom-right-radius: 0;
}

.message.received.last-in-group .message-content {
  border-bottom-left-radius: 0;
}

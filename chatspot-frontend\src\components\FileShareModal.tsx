import React, { useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { sendMessageRequest, selectConnected } from '../redux/slices/socketSlice';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faImage, 
  faVideo, 
  faFile, 
  faTimes, 
  faUpload,
  faPlay,
  faFileVideo
} from '@fortawesome/free-solid-svg-icons';
import './FileShareModal.css';

interface FileShareModalProps {
  receiverUsername: string;
  onClose: () => void;
}

interface FilePreview {
  file: File;
  url: string;
  type: 'image' | 'video' | 'file';
}

const FileShareModal: React.FC<FileShareModalProps> = ({ receiverUsername, onClose }) => {
  const dispatch = useDispatch();
  const connected = useSelector(selectConnected);
  const [selectedFile, setSelectedFile] = useState<FilePreview | null>(null);
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // File type validation
  const isValidFileType = (file: File): boolean => {
    const validTypes = {
      image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      video: ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'],
      file: [] // Allow all other file types
    };

    return (
      validTypes.image.includes(file.type) ||
      validTypes.video.includes(file.type) ||
      (!validTypes.image.includes(file.type) && !validTypes.video.includes(file.type))
    );
  };

  // Get file type category
  const getFileType = (file: File): 'image' | 'video' | 'file' => {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type.startsWith('video/')) return 'video';
    return 'file';
  };

  // Handle file selection
  const handleFileSelect = (file: File) => {
    if (!isValidFileType(file)) {
      alert('Unsupported file type');
      return;
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      alert('File size must be less than 50MB');
      return;
    }

    const url = URL.createObjectURL(file);
    const type = getFileType(file);

    setSelectedFile({
      file,
      url,
      type
    });
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Upload file and send message
  const handleSendFile = async () => {
    if (!selectedFile || !connected || !receiverUsername) return;

    setUploading(true);
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', selectedFile.file);
      formData.append('receiverUsername', receiverUsername);

      // Upload file to backend
      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const uploadResult = await response.json();

      // Send message with file information
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: selectedFile.file.name,
        messageType: selectedFile.type,
        fileData: {
          fileUrl: uploadResult.fileUrl,
          fileName: selectedFile.file.name,
          fileSize: selectedFile.file.size,
          fileType: selectedFile.file.type,
          thumbnailUrl: uploadResult.thumbnailUrl
        }
      }));

      // Clean up and close modal
      URL.revokeObjectURL(selectedFile.url);
      setSelectedFile(null);
      onClose();
    } catch (error) {
      console.error('File upload failed:', error);
      alert('Failed to upload file. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Clear selected file
  const clearSelectedFile = () => {
    if (selectedFile) {
      URL.revokeObjectURL(selectedFile.url);
      setSelectedFile(null);
    }
  };

  return (
    <div className="file-share-modal-overlay" onClick={onClose}>
      <div className="file-share-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Share File</h3>
          <button className="close-button" onClick={onClose}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <div className="modal-content">
          {!selectedFile ? (
            <div 
              className={`file-drop-zone ${dragOver ? 'drag-over' : ''}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="drop-zone-content">
                <FontAwesomeIcon icon={faUpload} size="3x" />
                <h4>Drop files here or click to browse</h4>
                <p>Supports images, videos, and documents (max 50MB)</p>
                
                <div className="file-type-icons">
                  <div className="file-type-icon">
                    <FontAwesomeIcon icon={faImage} />
                    <span>Images</span>
                  </div>
                  <div className="file-type-icon">
                    <FontAwesomeIcon icon={faVideo} />
                    <span>Videos</span>
                  </div>
                  <div className="file-type-icon">
                    <FontAwesomeIcon icon={faFile} />
                    <span>Documents</span>
                  </div>
                </div>
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*,video/*,*/*"
                onChange={handleFileInputChange}
                style={{ display: 'none' }}
              />
            </div>
          ) : (
            <div className="file-preview">
              <div className="preview-header">
                <h4>Selected File</h4>
                <button className="clear-button" onClick={clearSelectedFile}>
                  <FontAwesomeIcon icon={faTimes} />
                </button>
              </div>
              
              <div className="preview-content">
                {selectedFile.type === 'image' && (
                  <img 
                    src={selectedFile.url} 
                    alt="Preview" 
                    className="image-preview"
                  />
                )}
                
                {selectedFile.type === 'video' && (
                  <div className="video-preview">
                    <video 
                      src={selectedFile.url} 
                      controls 
                      className="video-preview-player"
                    />
                    <div className="video-overlay">
                      <FontAwesomeIcon icon={faPlay} size="2x" />
                    </div>
                  </div>
                )}
                
                {selectedFile.type === 'file' && (
                  <div className="file-preview-info">
                    <FontAwesomeIcon icon={faFileVideo} size="3x" />
                    <div className="file-details">
                      <h5>{selectedFile.file.name}</h5>
                      <p>{formatFileSize(selectedFile.file.size)}</p>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="file-info">
                <p><strong>Name:</strong> {selectedFile.file.name}</p>
                <p><strong>Size:</strong> {formatFileSize(selectedFile.file.size)}</p>
                <p><strong>Type:</strong> {selectedFile.file.type}</p>
              </div>
            </div>
          )}
        </div>

        <div className="modal-actions">
          <button className="cancel-button" onClick={onClose}>
            Cancel
          </button>
          {selectedFile && (
            <button 
              className="send-button" 
              onClick={handleSendFile}
              disabled={uploading || !connected}
            >
              {uploading ? 'Uploading...' : 'Send File'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileShareModal;

# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/

# Production
build/
dist/
out/
.next/

# Environment variables
# .env files are now included in source control
# .env.local
# .env.development.local
# .env.test.local
# .env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Mobile specific
.expo/
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# macOS
.DS_Store

# Temporary files
*.tmp
*.temp

# Database
*.sqlite
*.sqlite3
*.db

# Compiled output
*.class
*.jar
*.war
*.ear
*.dll
*.exe
*.o
*.so

# Dependency directories
jspm_packages/
bower_components/

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Parcel bundler cache
.cache/
.parcel-cache/

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt/
dist/

# Gatsby files
.cache/
public/

# vuepress build output
.vuepress/dist/

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

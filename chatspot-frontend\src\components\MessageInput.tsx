import { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { sendMessageRequest, selectConnected, selectError } from '../redux/slices/socketSlice';
import './MessageInput.css';
import { RootState } from '../redux/store';
import EmojiBar from './EmojiBar';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPaperclip } from '@fortawesome/free-solid-svg-icons';

interface MessageInputProps {
  receiverUsername: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({ receiverUsername, onFocus, onBlur }) => {
  const dispatch = useDispatch();
  const connected = useSelector(selectConnected);
  const error = useSelector(selectError);

  const [message, setMessage] = useState<string>('');
  const [sendStatus, setSendStatus] = useState<{ success: boolean, message: string } | null>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.scrollIntoView({ block: 'nearest' });
    }
  }, [message]);

  // Watch for errors from Redux
  useEffect(() => {
    if (error && error.includes('Failed to send message')) {
      setSendStatus({
        success: false,
        message: error
      });
    }
  }, [error]);

  // Clear send status after 3 seconds
  useEffect(() => {
    if (sendStatus) {
      const timer = setTimeout(() => {
        setSendStatus(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [sendStatus]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || !receiverUsername || !connected) {
      if (!connected) {
        setSendStatus({
          success: false,
          message: 'Not connected to server'
        });
      }
      return;
    }

    // Send as a text message type
    dispatch(sendMessageRequest({
      receiverUsername,
      messageText: message.trim(),
      messageType: 'text'
    }));
    setMessage('');

    // Focus back on the input after sending
    if (inputRef.current) {
      inputRef.current.focus();
      // Reset height
      inputRef.current.style.height = 'auto';
    }
  };

  // Typing indicator state
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Send typing indicator
  const sendTypingIndicator = (typing: boolean) => {
    if (connected && receiverUsername) {
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: typing ? 'typing' : 'stopped_typing',
        messageType: 'typing'
      }));
    }
  };

  // Handle textarea height adjustment and typing indicator
  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const target = e.target;
    const newValue = target.value;
    setMessage(newValue);

    // Reset height to auto to properly calculate the new height
    target.style.height = 'auto';

    // Set new height based on scrollHeight (with a max height)
    // Ensure a minimum height of 44px (or 40px on mobile)
    // const minHeight = window.innerWidth <= 767 ? 40 : 40;
    // const newHeight = Math.max(minHeight, Math.min(target.scrollHeight, 120));
    // target.style.height = `${newHeight}px`;

    // Handle typing indicator
    if (newValue.trim() && !isTyping) {
      // User started typing
      setIsTyping(true);
      sendTypingIndicator(true);
    } else if (!newValue.trim() && isTyping) {
      // User stopped typing
      setIsTyping(false);
      sendTypingIndicator(false);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop the typing indicator after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTypingIndicator(false);
      }
    }, 3000);
  };

  // Set initial height and handle window resize
  useEffect(() => {
    // const setInitialHeight = () => {
    //   if (inputRef.current) {
    //     const minHeight = window.innerWidth <= 767 ? 40 : 40;
    //     inputRef.current.style.height = `${minHeight}px`;
    //   }
    // };

    // Set initial height
    // setInitialHeight();

    // Add resize listener to adjust height on window resize
    // window.addEventListener('resize', setInitialHeight);

    // Clean up
    return () => {
      // window.removeEventListener('resize', setInitialHeight);

      // Clear typing timeout and send stopped typing on unmount
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      if (isTyping && connected && receiverUsername) {
        sendTypingIndicator(false);
      }
    };
  }, [isTyping, connected, receiverUsername]);

  // Handle Enter key to submit (Shift+Enter for new line)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    console.log(e)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Handle emoji click
  const handleEmojiClick = (emoji: string) => {

  };

  // Handle emoji long press
  const handleEmojiLongPress = (emoji: string) => {
    if (connected && receiverUsername) {
      // Get the mood name from the emoji
      let mood = '';
      switch (emoji) {
        case '😊': mood = 'happy'; break;
        case '😂': mood = 'laughing'; break;
        case '😡': mood = 'angry'; break;
        case '😢': mood = 'sad'; break;
        case '❤️': mood = 'love'; break;
        case '👍': mood = 'thumbsUp'; break;
        default: mood = 'feeling';
      }
      console.log('emoji pressed')
      // Send emoji reaction message with emoji and mood
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: `${emoji}:${mood}`,
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle emoji release
  const handleEmojiRelease = () => {
    if (connected && receiverUsername) {
      // Send message to stop showing emoji reaction
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'stopped_reaction',
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle attachment button click
  const handleAttachmentClick = () => {
    // Create a native-like alert with options
    const options = [
      { label: 'Camera', action: () => handleCameraCapture() },
      { label: 'Photo Library', action: () => handleImagePicker() },
      { label: 'Video Library', action: () => handleVideoPicker() },
      { label: 'Cancel', action: () => {} }
    ];

    // Create a simple modal-like interface
    const modal = document.createElement('div');
    modal.className = 'attachment-modal-overlay';
    modal.innerHTML = `
      <div class="attachment-modal">
        <div class="attachment-modal-header">
          <h3>Select Media</h3>
        </div>
        <div class="attachment-modal-options">
          <button class="attachment-option" data-action="camera">
            📷 Camera
          </button>
          <button class="attachment-option" data-action="image">
            🖼️ Photo Library
          </button>
          <button class="attachment-option" data-action="video">
            🎥 Video Library
          </button>
          <button class="attachment-option cancel" data-action="cancel">
            Cancel
          </button>
        </div>
      </div>
    `;

    // Add event listeners
    modal.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('attachment-modal-overlay') || target.dataset.action === 'cancel') {
        document.body.removeChild(modal);
      } else if (target.dataset.action === 'camera') {
        handleCameraCapture();
        document.body.removeChild(modal);
      } else if (target.dataset.action === 'image') {
        handleImagePicker();
        document.body.removeChild(modal);
      } else if (target.dataset.action === 'video') {
        handleVideoPicker();
        document.body.removeChild(modal);
      }
    });

    document.body.appendChild(modal);
  };

  // Handle camera capture
  const handleCameraCapture = () => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = 'image/*';
      fileInputRef.current.capture = 'environment';
      fileInputRef.current.click();
    }
  };

  // Handle image picker
  const handleImagePicker = () => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = 'image/*';
      fileInputRef.current.removeAttribute('capture');
      fileInputRef.current.click();
    }
  };

  // Handle video picker
  const handleVideoPicker = () => {
    if (videoInputRef.current) {
      videoInputRef.current.click();
    }
  };

  // Handle file selection
  const handleFileSelect = async (file: File, type: 'image' | 'video') => {
    if (!connected || !receiverUsername) return;

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('receiverUsername', receiverUsername);
      formData.append('type', type);

      // Upload file to backend
      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const uploadResult = await response.json();

      // Send message with file information
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: file.name,
        messageType: type,
        fileData: {
          fileUrl: uploadResult.fileUrl,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          thumbnailUrl: uploadResult.thumbnailUrl
        }
      }));

      setSendStatus({
        success: true,
        message: `${type === 'image' ? 'Image' : 'Video'} sent successfully`
      });
    } catch (error) {
      console.error('File upload failed:', error);
      setSendStatus({
        success: false,
        message: 'Failed to upload file'
      });
    }
  };

  // Handle image file input change
  const handleImageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file, 'image');
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  // Handle video file input change
  const handleVideoInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (limit to 100MB for videos)
      if (file.size > 100 * 1024 * 1024) {
        setSendStatus({
          success: false,
          message: 'Video file size must be less than 100MB'
        });
        return;
      }
      handleFileSelect(file, 'video');
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  return (
    <div className="message-input-wrapper">
      {sendStatus && (
        <div className={`send-status ${sendStatus.success ? 'success' : 'error'}`}>
          {sendStatus.message}
        </div>
      )}

      {/* Emoji Bar */}
      <EmojiBar
        onEmojiClick={handleEmojiClick}
        onEmojiLongPress={handleEmojiLongPress}
        onEmojiRelease={handleEmojiRelease}
      />

      <form className="message-input-form" onSubmit={handleSubmit}>
        <button
          type="button"
          onClick={handleAttachmentClick}
          disabled={!connected || !receiverUsername}
          className="attachment-button"
          title="Attach media"
        >
          <FontAwesomeIcon icon={faPaperclip} />
        </button>

        <textarea
          ref={inputRef}
          value={message}
          onChange={handleInput}
          onKeyDown={handleKeyDown}
          placeholder={connected ? 'Type a message...' : 'Connect to send messages'}
          disabled={!connected || !receiverUsername}
          className="message-input"
          rows={1}
          onFocus={onFocus}
          onBlur={onBlur}
        />

        <button
          type="submit"
          disabled={!connected || !message.trim() || !receiverUsername}
          className="send-button"
          title="Send message"
        >
          <svg
            viewBox="0 0 12800 12780"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            preserveAspectRatio="xMidYMid meet"
            fill="currentColor"
            style={{ transform: "rotate(-90deg)" }}
          >
            <path d="M2835 12432 c-752 -746 -2825 -2815 -2825 -2818 0 -2 1635 -4 3633
  -4 l3632 0 -3578 -3577 c-1967 -1968 -3577 -3580 -3577 -3583 0 -3 522 -527
  1160 -1165 l1160 -1160 3588 3588 c1973 1973 3590 3587 3594 3587 5 0 9 -1640
  10 -3643 l3 -3644 1583 1576 1582 1576 0 4808 0 4807 -4807 0 -4808 0 -350
  -348z"/>
          </svg>
        </button>

        {/* Hidden file inputs */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageInputChange}
          style={{ display: 'none' }}
        />
        <input
          ref={videoInputRef}
          type="file"
          accept="video/*"
          onChange={handleVideoInputChange}
          style={{ display: 'none' }}
        />
      </form>
    </div>
  );
};

export default MessageInput;

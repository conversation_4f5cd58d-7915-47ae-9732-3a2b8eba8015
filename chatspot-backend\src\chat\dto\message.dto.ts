import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsOptional } from 'class-validator';

export class MessageDto {
  @ApiProperty({
    description: 'Unique identifier for the message',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Username of the sender',
    example: 'johndoe',
  })
  @IsString()
  @IsNotEmpty()
  sender_username: string;

  @ApiProperty({
    description: 'Username of the receiver',
    example: 'janedoe',
  })
  @IsString()
  @IsNotEmpty()
  receiver_username: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Timestamp when the message was sent',
    example: '2023-10-15T14:30:00Z',
  })
  timestamp: Date;

  @ApiProperty({
    description: 'Status of the message',
    example: 'delivered',
    enum: ['pending', 'delivered'],
  })
  status: 'pending' | 'delivered';

  @ApiProperty({
    description: 'Timestamp when the message was delivered',
    example: '2023-10-15T14:30:05Z',
    nullable: true,
  })
  delivered_at: Date | null;

  @ApiProperty({
    description: 'Message type',
    example: 'text',
    enum: ['text', 'clear_chat', 'typing', 'delete_user', 'system'],
    default: 'text',
  })
  @IsString()
  @IsOptional()
  type?: 'text' | 'clear_chat' | 'typing' | 'delete_user' | 'system';
}

import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Body,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as fs from 'fs';

// Configure multer for file storage
const storage = diskStorage({
  destination: './uploads',
  filename: (req, file, callback) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = extname(file.originalname);
    callback(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  },
});

// File filter for allowed types
const fileFilter = (req: any, file: any, callback: any) => {
  const allowedTypes = /jpeg|jpg|png|gif|webp|mp4|webm|ogg|avi|mov|pdf|doc|docx|txt/;
  const extName = allowedTypes.test(extname(file.originalname).toLowerCase());
  const mimeType = allowedTypes.test(file.mimetype);

  if (mimeType && extName) {
    return callback(null, true);
  } else {
    callback(new BadRequestException('Invalid file type'), false);
  }
};

@ApiTags('upload')
@Controller('api/upload')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UploadController {
  constructor() {
    // Ensure uploads directory exists
    if (!fs.existsSync('./uploads')) {
      fs.mkdirSync('./uploads', { recursive: true });
    }
  }

  @Post()
  @ApiOperation({ summary: 'Upload a file' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 201,
    description: 'File uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        fileUrl: { type: 'string' },
        fileName: { type: 'string' },
        fileSize: { type: 'number' },
        fileType: { type: 'string' },
        thumbnailUrl: { type: 'string', nullable: true },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid file type or size' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseInterceptors(
    FileInterceptor('file', {
      storage,
      fileFilter,
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB limit
      },
    }),
  )
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { receiverUsername: string; type: string },
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Generate file URL (you might want to use a proper file serving setup)
    const fileUrl = `/uploads/${file.filename}`;
    
    // For images, you could generate thumbnails here
    let thumbnailUrl: string | null = null;
    if (file.mimetype.startsWith('image/')) {
      // For now, use the same image as thumbnail
      // In production, you'd want to generate actual thumbnails
      thumbnailUrl = fileUrl;
    }

    return {
      fileUrl,
      fileName: file.originalname,
      fileSize: file.size,
      fileType: file.mimetype,
      thumbnailUrl,
    };
  }
}

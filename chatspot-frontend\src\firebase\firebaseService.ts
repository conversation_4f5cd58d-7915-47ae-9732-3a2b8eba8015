import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage, isSupported } from 'firebase/messaging';
import { firebaseConfig, vapidKey } from './config';

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Check if messaging is supported in this browser
const isFCMSupported = async (): Promise<boolean> => {
  try {
    return await isSupported();
  } catch (error) {
    console.error('Firebase Messaging not supported:', error);
    return false;
  }
};

// Firebase Messaging Service
export const firebaseMessagingService = {
  // Initialize Firebase Messaging
  initialize: async (): Promise<boolean> => {
    try {
      if (!(await isFCMSupported())) {
        console.warn('Firebase Cloud Messaging is not supported in this browser');
        return false;
      }
      
      // Request permission and get token
      await firebaseMessagingService.requestPermission();
      return true;
    } catch (error) {
      console.error('Failed to initialize Firebase Messaging:', error);
      return false;
    }
  },

  // Request notification permission
  requestPermission: async (): Promise<boolean> => {
    try {
      if (!(await isFCMSupported())) return false;
      
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      return false;
    }
  },

  // Get FCM token
  getToken: async (): Promise<string | null> => {
    try {
      if (!(await isFCMSupported())) return null;
      
      const messaging = getMessaging(app);
      const currentToken = await getToken(messaging, { vapidKey });
      
      if (currentToken) {
        console.log('FCM token:', currentToken);
        return currentToken;
      } else {
        console.warn('No FCM token available');
        return null;
      }
    } catch (error) {
      console.error('Failed to get FCM token:', error);
      return null;
    }
  },

  // Listen for foreground messages
  onMessage: (callback: (payload: any) => void): (() => void) => {
    try {
      const messaging = getMessaging(app);
      return onMessage(messaging, callback);
    } catch (error) {
      console.error('Failed to set up message listener:', error);
      return () => {};
    }
  },
};

export default firebaseMessagingService;

import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { userService } from '../services/userService';
import Box from './Box';
import Text from './Text';
import { useTheme } from '@shopify/restyle';
import { Theme } from '../theme/theme';

interface UserInfoProps {
  username: string;
  size?: 'small' | 'medium' | 'large';
  showAvatar?: boolean;
  showName?: boolean;
}

interface UserData {
  id: string;
  username: string;
  // Add other user properties as needed
}

export const UserInfo: React.FC<UserInfoProps> = ({
  username,
  size = 'medium',
  showAvatar = true,
  showName = true,
}) => {
  const theme = useTheme<Theme>();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // We don't need to fetch user info since we already have the username
  useEffect(() => {
    setLoading(false);
    setError(null);
    // Create a simple user data object with the username
    setUserData({
      id: '', // We don't need the ID
      username: username
    });
  }, [username]);

  // Get avatar size based on the size prop
  const getAvatarSize = () => {
    switch (size) {
      case 'small':
        return 36;
      case 'large':
        return 56;
      case 'medium':
      default:
        return 48;
    }
  };

  // Display first letter of username as avatar
  const getAvatarText = () => {
    return username ? username.charAt(0).toUpperCase() : '?';
  };

  // Display username
  const getDisplayName = () => {
    return username || 'Unknown';
  };

  const avatarSize = getAvatarSize();

  return (
    <Box flexDirection="row" alignItems="center">
      {showAvatar && (
        <Box
          width={avatarSize}
          height={avatarSize}
          borderRadius="full"
          backgroundColor="primary"
          justifyContent="center"
          alignItems="center"
          marginRight="s"
        >
          <Text variant="title" color="white">
            {getAvatarText()}
          </Text>
        </Box>
      )}

      {showName && (
        <Box flex={1}>
          <Text variant="bodyBold" numberOfLines={1}>
            {getDisplayName()}
          </Text>
        </Box>
      )}
    </Box>
  );
};

export default UserInfo;

import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useSelector, useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { selectIsAuthenticated, initializeAuth, selectAuthToken } from '../redux/slices/authSlice';
import { setServerUrl, connectRequest } from '../redux/slices/socketSlice';
import { setApiBaseUrl } from '../services/apiClient';
import ENV from '../config/env';

// Import screens
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import RoomsScreen from '../screens/RoomsScreen';
import ChatScreen from '../screens/ChatScreen';

// Create stack navigators
const AuthStack = createNativeStackNavigator();
const MainStack = createNativeStackNavigator();

// Auth navigator
const AuthNavigator = () => {
  return (
    <AuthStack.Navigator screenOptions={{ headerShown: false }}>
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Register" component={RegisterScreen} />
    </AuthStack.Navigator>
  );
};

// Main app navigator
const MainNavigator = () => {
  return (
    <MainStack.Navigator screenOptions={{ headerShown: false }}>
      <MainStack.Screen name="Rooms" component={RoomsScreen} />
      <MainStack.Screen name="Chat" component={ChatScreen} />
    </MainStack.Navigator>
  );
};

// Root navigator
export const AppNavigator = () => {
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authToken = useSelector(selectAuthToken);

  // Initialize auth state from AsyncStorage
  useEffect(() => {
    const loadAuthData = async () => {
      try {
        const token = await AsyncStorage.getItem('auth_token');
        const username = await AsyncStorage.getItem('auth_username');

        dispatch(initializeAuth({ token, username }));
      } catch (error) {
        console.error('Failed to load auth data:', error);
      }
    };

    loadAuthData();
  }, [dispatch]);

  // Set API and WebSocket URLs from environment configuration
  useEffect(() => {
    setApiBaseUrl(ENV.API_URL);
    dispatch(setServerUrl(ENV.WS_URL));
  }, [dispatch]);

  // Connect to socket when app starts and user is authenticated
  useEffect(() => {
    if (isAuthenticated && authToken) {
      console.log('Connecting to socket on app start...');
      dispatch(connectRequest({ authToken }));
    }
  }, [isAuthenticated, authToken, dispatch]);

  return (
    <NavigationContainer>
      {isAuthenticated ? <MainNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

export default AppNavigator;
